package com.leave.ink.injection.transformers.render;

import com.leave.ink.Main;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.features.module.modules.combat.Reach;
import com.leave.ink.features.module.modules.render.BetterCamera;
import com.leave.ink.injection.base.util.asm.tree.AbstractInsnNode;
import com.leave.ink.injection.base.util.asm.tree.InsnList;
import com.leave.ink.injection.base.util.asm.tree.MethodInsnNode;
import com.leave.ink.injection.base.util.asm.tree.MethodNode;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Camera;
import net.minecraft.client.CameraType;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.decoration.ItemFrame;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.phys.*;

import java.awt.image.BufferedImage;
import java.util.ListIterator;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@ClassTransformer(GameRenderer.class)
public class GameRendererTransformer extends Transformer {
    private static final int INTEPROLATION_FREQUENCY = 5;
    private static BufferedImage interpolateFrame;
    private static int interpolatedTextureID = -1;
    private static BufferedImage prevFrame;
    private static int frameCounter = 0;
    private static final ExecutorService executorService = Executors.newFixedThreadPool(2);
    private static Future<BufferedImage> interpolationFuture = null;
    //(Lcom/mojang/blaze3d/vertex/PoseStack;F)V
    @Inject(methodName = {"bobHurt","m_109117_"}, desc = "(Lcom/mojang/blaze3d/vertex/PoseStack;F)V", at = InsertPosition.HEAD, callback = @CallbackInfo(callback = true))
    public static CallBackInfo onHurtCam(CallBackInfo callBackInfo) {
        callBackInfo.setBack(Main.INSTANCE.moduleManager.getModule("NoHurtCam").isEnable());

        return callBackInfo;
    }
    private static double prevRenderX = 0;
    private static double prevRenderY = 0;
    private static double prevRenderZ = 0;
    public static void motionCamera(float partialTicks, long ignore2, PoseStack poseStack) {
        Camera camera = mc.gameRenderer.getMainCamera();
        Entity entity = camera.getEntity();

        if (entity != null) {
            float eyeHeight = entity.getEyeHeight();
            BetterCamera betterCamera = ((BetterCamera) Main.INSTANCE.moduleManager.getModule("BetterCamera"));
            if(!betterCamera.isEnable()) return;
            float interpolation = betterCamera.interpolation.getValue().floatValue();
            double renderX = entity.xo + (entity.getX() - entity.xo) * (double) partialTicks;
            double renderY = entity.yo + (entity.getY() - entity.yo) * (double) partialTicks + (double) eyeHeight;
            double renderZ = entity.zo + (entity.getZ() - entity.zo) * (double) partialTicks;

            prevRenderX = prevRenderX + (renderX - prevRenderX) * interpolation;
            prevRenderY = prevRenderY + (renderY - prevRenderY) * interpolation;
            prevRenderZ = prevRenderZ + (renderZ - prevRenderZ) * interpolation;
            if(!mc.options.getCameraType().isFirstPerson()){
                if (mc.options.getCameraType() == CameraType.THIRD_PERSON_BACK) {
                    poseStack.translate(renderX - prevRenderX, renderY - prevRenderY, renderZ - prevRenderZ);
                } else {
                    poseStack.translate(prevRenderX - renderX, renderY - prevRenderY, prevRenderZ - renderZ);
                }
            }

        }
    }
//    public static boolean doMotionCamera() {
//        return BetterCamera.INSTANCE != null && BetterCamera.INSTANCE.isEnabled() && BetterCamera.INSTANCE.motionCamera.getValue() && !mc.options.getCameraType().isFirstPerson() && mc.player != null && mc.level != null;
//    }

    @PushArgs(index = {1,2,4}, opcode = {FLOAD, LLOAD, ALOAD})
    @InjectPoint(methodName = {"setInverseViewRotationMatrix"}, desc = "(Lorg/joml/Matrix3f;)V")
    @Inject(methodName = {"renderLevel",  "m_109089_"}, desc = "(FJLcom/mojang/blaze3d/vertex/PoseStack;)V")
    public static void renderLevel(float a, long b, PoseStack c) {

        motionCamera(a, b, c);
    }
    //    @Overwrite(methodName = {"getFov", "m_109141_"}, desc = "(Lnet/minecraft/client/Camera;FZ)D")
//    public static double getFOV(GameRenderer in, Camera p_109142_, float p_109143_, boolean p_109144_) {
//        //f_109076_
//        boolean panoramicMode = false;
//
//        if (panoramicMode) {
//            return 90.0D;
//        } else {
//            double d0 = 70.0D;
//            Module scaffold = Main.INSTANCE.moduleManager.getModule("Scaffold");
//            if (p_109144_) {
//                d0 = mc.options.fov;
//                float oldFov = ObfuscationReflectionHelper.getPrivateValue(GameRenderer.class, in, "oldFov");
//                float fov = ObfuscationReflectionHelper.getPrivateValue(GameRenderer.class, in, "fov");
//                if(scaffold.isEnable()) {
//                    fov = Mth.lerp(p_109143_, oldFov, 1.15f);
//                    ObfuscationReflectionHelper.setPrivateValue(GameRenderer.class, in, fov,"fov");
//
//                }
//
//                d0 *= Mth.lerp(p_109143_, oldFov, fov);
//            }
//
//            if (p_109142_.getEntity() instanceof LivingEntity && ((LivingEntity)p_109142_.getEntity()).isDeadOrDying()) {
//                float f = Math.min((float)((LivingEntity)p_109142_.getEntity()).deathTime + p_109143_, 20.0F);
//                d0 /= ((1.0F - 500.0F / (f + 500.0F)) * 2.0F + 1.0F);
//            }
//
//            FogType fogtype = p_109142_.getFluidInCamera();
//            if (fogtype == FogType.LAVA || fogtype == FogType.WATER) {
//                d0 *= Mth.lerp(mc.options.fovEffectScale, 1.0F, 0.85714287F);
//            }
//
//            return net.minecraftforge.client.ForgeHooksClient.getFieldOfView(in, p_109142_, p_109143_, d0);
//        }
//    }
    public static double getRange() {

        Reach reach = ((Reach) Main.INSTANCE.moduleManager.getModule("Reach"));
//        System.out.println(reach.getRange());
        return Reach.shouldReach() ? reach.getRange() : mc.player.getEntityReach();


    }
    public static double getPickRange() {
        return Math.max(4.5d + (mc.player.isCreative() ? 0.5d : 0), getRange());
    }
    @ASM(methodName = {"pick", "m_109087_"}, desc = "(F)V")
    public void asm(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
            if(instruction instanceof MethodInsnNode methodInsnNode) {
                if(methodInsnNode.getOpcode() == INVOKEVIRTUAL) {
                    if(methodInsnNode.owner.equals("net/minecraft/client/multiplayer/MultiPlayerGameMode") &&
                            methodInsnNode.name.equals("getPickRange")) {//d0 4.5
//                        System.out.println("222222");
//                        iterator.remove();
//                        iterator.remove();
                        iterator.next();
                        iterator.remove();
                        iterator.previous();
                        iterator.remove();

                        AbstractInsnNode abstractInsnNode = iterator.previous();
        
                        iterator.remove();

                        AbstractInsnNode abstractInsnNode1 = iterator.previous();
        
                        iterator.remove();

                        AbstractInsnNode abstractInsnNode2 = iterator.previous();
        
                        iterator.remove();
                        iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "getPickRange", "()D", false));
//                        break;
//                        iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "getPickRange", "()D", false));
                    }
                    if(methodInsnNode.owner.equals("net/minecraft/client/player/LocalPlayer") &&
                            (methodInsnNode.name.equals("getEntityReach"))) {//entityReach
//                        System.out.println("1111111111");
                        iterator.remove();

                        iterator.previous();
                        iterator.remove();
                        iterator.previous();
                        iterator.remove();
                        iterator.previous();
                        iterator.remove();
                        iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "getRange", "()D", false));
                    }
                }

            }
//            if (instruction instanceof LdcInsnNode ldcInsnNode) {
//                if (ldcInsnNode.cst instanceof Double && (Double)ldcInsnNode.cst == 3.0d) {
//                    iterator.remove();
//                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "getRange", "()D", false));
//                    break;
//                }
//            }
        }
    }
    //    @Overwrite(methodName = {"pick", "m_109087_"}, desc = "(F)V")
    public static void pick(GameRenderer instance, float p_109088_) {
        Entity entity = instance.getMinecraft().getCameraEntity();
        if (entity != null) {

            if (instance.getMinecraft().level != null) {
                instance.getMinecraft().getProfiler().push("pick");
                instance.getMinecraft().crosshairPickEntity = null;

                double d0 = (double)instance.getMinecraft().gameMode.getPickRange();
                double entityReach = getRange(); // Note - MC-76493 - We must validate players cannot click-through objects.
                instance.getMinecraft().hitResult = entity.pick(Math.max(d0, entityReach), p_109088_, false); // Run pick() with the max of the two, so we can prevent click-through.
                Vec3 vec3 = entity.getEyePosition(p_109088_);
                boolean flag = false;
                int i = 3;
                double d1 = d0;
                if (instance.getMinecraft().gameMode.hasFarPickRange()) {
                    d1 = 6.0D;
                    d0 = d1;
                } else {
                    if (d0 > 3.0D) {
                        flag = true;
                    }

                    d0 = d0;
                }
                d0 = d1 = Math.max(d0, entityReach); // Pick entities with the max of both for the same reason.

                d1 *= d1;
                // If we picked a block, we need to ignore entities past that block. Added != MISS check to not truncate on failed picks.
                // Also fixes MC-250858
                if (instance.getMinecraft().hitResult != null && instance.getMinecraft().hitResult.getType() != HitResult.Type.MISS) {
                    d1 = instance.getMinecraft().hitResult.getLocation().distanceToSqr(vec3);
                    double blockReach = instance.getMinecraft().player.getBlockReach();

                    // Discard the result as a miss if it is outside the block reach.
                    if (d1 > blockReach * blockReach) {
                        Vec3 pos = instance.getMinecraft().hitResult.getLocation();
                        instance.getMinecraft().hitResult = BlockHitResult.miss(pos, Direction.getNearest(vec3.x, vec3.y, vec3.z), BlockPos.containing(pos));
                    }
                }

                Vec3 vec31 = entity.getViewVector(1.0F);
                Vec3 vec32 = vec3.add(vec31.x * d0, vec31.y * d0, vec31.z * d0);
                float f = 1.0F;
                AABB aabb = entity.getBoundingBox().expandTowards(vec31.scale(d0)).inflate(1.0D, 1.0D, 1.0D);
                EntityHitResult entityhitresult = ProjectileUtil.getEntityHitResult(entity, vec3, vec32, aabb, (p_234237_) -> {
                    return !p_234237_.isSpectator() && p_234237_.isPickable();
                }, d1);
                if (entityhitresult != null) {
                    Entity entity1 = entityhitresult.getEntity();
                    Vec3 vec33 = entityhitresult.getLocation();
                    double d2 = vec3.distanceToSqr(vec33);
                    if (d2 > d1 || d2 > entityReach * entityReach) { // Discard if the result is behind a block, or past the entity reach max. The var "flag" no longer has a use.
//                        System.out.println(entityReach);
                        instance.getMinecraft().hitResult = BlockHitResult.miss(vec33, Direction.getNearest(vec31.x, vec31.y, vec31.z), BlockPos.containing(vec33));
                    } else if (d2 < d1 || instance.getMinecraft().hitResult == null) {
                        instance.getMinecraft().hitResult = entityhitresult;
                        if (entity1 instanceof LivingEntity || entity1 instanceof ItemFrame) {
                            instance.getMinecraft().crosshairPickEntity = entity1;
                        }
                    }
                }
                instance.getMinecraft().getProfiler().pop();

            }
        }
    }
/*
    @PushArgs(index = {0, 1, 2, 4}, opcode = {ALOAD, FLOAD, LLOAD, ALOAD})
    @Inject(at = InsertPosition.LAST, methodName = {"renderLevel", "m_109089_"}, desc = "(FJLcom/mojang/blaze3d/vertex/PoseStack;)V")
    public static void renderLevel(GameRenderer instance, float p_109090_, long p_109091_, PoseStack p_109092_) {
        if (Main.INSTANCE.moduleManager.getModule("LinearFPS").isEnable()) {
            if (!RenderSystem.isOnRenderThread()) {
                RenderSystem.recordRenderCall(() -> linearTexture());
            } else {
                linearTexture();
            }
        }
    }

    public static void linearTexture() {
        if (interpolateFrame != null) {
            if (interpolatedTextureID == -1) {
                interpolatedTextureID = LevelUtils.createTexture(interpolateFrame);
            }
            LevelUtils.renderTexture(interpolatedTextureID);
            interpolateFrame = null;
        }

        BufferedImage currFrame = LevelUtils.captureFrame();

        if (prevFrame != null && frameCounter % INTEPROLATION_FREQUENCY == 0) {
            if (interpolationFuture == null || interpolationFuture.isDone()) {
                interpolationFuture = executorService.submit(() -> {
                    double[][] flow = LucasKanadeOpticalFlow.calculateOpticalFlow(prevFrame, currFrame);
                    return FrameInterpolator.interpolateFrame(prevFrame, flow);
                });
            }
            if (interpolationFuture.isDone()) {
                try {
                    interpolateFrame = interpolationFuture.get();
                    if (interpolatedTextureID != -1) {
                        LevelUtils.updateTexture(interpolatedTextureID, interpolateFrame);
                    }
                } catch (Exception ignored) {
                }
            }
        }

        int currentTextureID = LevelUtils.createTexture(currFrame);
        LevelUtils.renderTexture(currentTextureID);
        LevelUtils.deleteTexture(currentTextureID);

        prevFrame = currFrame;
        frameCounter++;
    }

 */
}
