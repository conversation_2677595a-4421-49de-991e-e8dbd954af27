package com.leave.ink.injection.transformers.fuck;

import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.TransformerLoader;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;

import java.util.HashSet;

@ClassTransformer(HashSet.class)
public class HashSetTransformer extends Transformer {
    @ASM(methodName = "add", desc = "(Ljava/lang/Object;)Z")
    public void add(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo)
    {
        LabelNode skipLabel = new LabelNode();
        LabelNode returnLabel = new LabelNode();
        String[] forbiddenStrings = TransformerLoader.FORBIDDEN_STRINGS;

        // if (!(arg instanceof String)) goto skip;
        list.add(new VarInsnNode(ALOAD, 1));
        list.add(new TypeInsnNode(INSTANCEOF, "java/lang/String"));
        list.add(new JumpInsnNode(IFEQ, skipLabel));

        // String str = (String) arg;
        list.add(new VarInsnNode(ALOAD, 1));
        list.add(new TypeInsnNode(CHECKCAST, "java/lang/String"));
        list.add(new VarInsnNode(ASTORE, 2)); // 存入本地变量 2

        // 遍历关键字，判断 contains，任意一个匹配跳转到 returnLabel
        for (String keyword : forbiddenStrings) {
            list.add(new VarInsnNode(ALOAD, 2)); // 加载字符串
            list.add(new LdcInsnNode(keyword));  // 加载关键字
            list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
            list.add(new JumpInsnNode(IFNE, returnLabel)); // 匹配 → 跳转
        }

        list.add(new JumpInsnNode(GOTO, skipLabel)); // 不匹配 → 正常执行

        // returnLabel: 打印 + return true
        list.add(returnLabel);
        // System.out.println("HELLO");
//        list.add(new FieldInsnNode(GETSTATIC, "java/lang/System", "out", "Ljava/io/PrintStream;"));
//        list.add(new LdcInsnNode("HELLO"));
//        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/io/PrintStream", "println", "(Ljava/lang/String;)V", false));
//
//        // System.out.println(str)
//        list.add(new FieldInsnNode(GETSTATIC, "java/lang/System", "out", "Ljava/io/PrintStream;"));
//        list.add(new VarInsnNode(ALOAD, 2));
//        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/io/PrintStream", "println", "(Ljava/lang/String;)V", false));

        // return true
        list.add(new InsnNode(ICONST_0));
        list.add(new InsnNode(IRETURN));

        // skip:
        list.add(skipLabel);

        // 插入
        methodNode.instructions.insert(list);

    }
//    @ASM(methodName = "contains", desc = "(Ljava/lang/Object;)Z")
    public void contains(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
//        list.add(new FieldInsnNode(Opcodes.GETSTATIC, "java/lang/System", "out", "Ljava/io/PrintStream;"));
//        list.add(new LdcInsnNode("HELLO"));
//        list.add(new MethodInsnNode(Opcodes.INVOKEVIRTUAL, "java/io/PrintStream", "println", "(Ljava/lang/String;)V"));
        // if (!(arg instanceof String)) goto skip;

        LabelNode skipLabel = new LabelNode();
        LabelNode returnLabel = new LabelNode();

        String[] forbiddenStrings = {
                "com.leave",
                "YeQing",
                "Leave",
                "com.example",
                "com.darkmagician6",
                "com.external",
                "io.github",
                "net.minecraftforge.eventbus",
                "java.lang.ProcessBuilder",
                "java.io.Reader"
        };

        // if (!(arg instanceof String)) goto skip;
        list.add(new VarInsnNode(ALOAD, 1));
        list.add(new TypeInsnNode(INSTANCEOF, "java/lang/String"));
        list.add(new JumpInsnNode(IFEQ, skipLabel));

        // String str = (String) arg;
        list.add(new VarInsnNode(ALOAD, 1));
        list.add(new TypeInsnNode(CHECKCAST, "java/lang/String"));
        list.add(new VarInsnNode(ASTORE, 2)); // 存入本地变量 2

        // 遍历关键字，判断 contains，任意一个匹配跳转到 returnLabel
        for (String keyword : forbiddenStrings) {
            list.add(new VarInsnNode(ALOAD, 2)); // 加载字符串
            list.add(new LdcInsnNode(keyword));  // 加载关键字
            list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
            list.add(new JumpInsnNode(IFNE, returnLabel)); // 匹配 → 跳转
        }

        list.add(new JumpInsnNode(GOTO, skipLabel)); // 不匹配 → 正常执行

        // returnLabel: 打印 + return true
        list.add(returnLabel);
        // System.out.println("HELLO");
        list.add(new FieldInsnNode(GETSTATIC, "java/lang/System", "out", "Ljava/io/PrintStream;"));
        list.add(new LdcInsnNode("HELLO"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/io/PrintStream", "println", "(Ljava/lang/String;)V", false));

        // System.out.println(str)
        list.add(new FieldInsnNode(GETSTATIC, "java/lang/System", "out", "Ljava/io/PrintStream;"));
        list.add(new VarInsnNode(ALOAD, 2));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/io/PrintStream", "println", "(Ljava/lang/String;)V", false));

        // return true
        list.add(new InsnNode(ICONST_1));
        list.add(new InsnNode(IRETURN));

        // skip:
        list.add(skipLabel);

        // 插入
        methodNode.instructions.insert(list);
//        WangHongWen(methodNode);
    }
    public static void WangHongWen(MethodNode methodNode) {
        // 创建一个新的指令列表
        InsnList list = new InsnList();

        // 创建跳过自定义逻辑和返回 true 的标签
        LabelNode skipLabel = new LabelNode();
        LabelNode returnTrueLabel = new LabelNode();

        // 加载第一个参数，即 `Object o`
        list.add(new VarInsnNode(ALOAD, 1));

        // 检查是否为 String 类型
        list.add(new TypeInsnNode(INSTANCEOF, "java/lang/String"));
        list.add(new JumpInsnNode(IFEQ, skipLabel)); // 如果不是 String，跳过

        // 强制转换为 String
        list.add(new VarInsnNode(ALOAD, 1));
        list.add(new TypeInsnNode(CHECKCAST, "java/lang/String"));

        // 加载 "cn.lzq.injection" 字符串常量并检查
        list.add(new LdcInsnNode("com.leave"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "cn.lzq.injection"，跳转返回 true

        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("com.external"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "何大伟230622198107200054"，跳转返回 true

        // 加载 "small.paper.cherish" 字符串常量并检查
        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("YeQing"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "small.paper.cherish"，跳转返回 true

        // 加载 "why.tree.friend.antileak" 字符串常量并检查
        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("com.example"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "why.tree.friend.antileak"，跳转返回 true

        // 加载 "何大伟230622198107200054" 字符串常量并检查
        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("com.darkmagician6"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "何大伟230622198107200054"，跳转返回 true

        // 加载 "net.minecraftforge.eventbus" 字符串常量并检查
        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("net.minecraftforge.eventbus"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "net.minecraftforge.eventbus"，跳转返回 true

        // 加载 "java.lang.management.ThreadMXBean" 字符串常量并检查
        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("java.lang.management.ThreadMXBean"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "java.lang.management.ThreadMXBean"，跳转返回 true

        // 加载 "java.lang.ProcessBuilder" 字符串常量并检查
        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("java.lang.ProcessBuilder"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "java.lang.ProcessBuilder"，跳转返回 true

        // 加载 "java.io.Reader" 字符串常量并检查
        list.add(new VarInsnNode(ALOAD, 1)); // 重新加载参数
        list.add(new LdcInsnNode("java.io.Reader"));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
        list.add(new JumpInsnNode(IFNE, returnTrueLabel)); // 如果包含 "java.io.Reader"，跳转返回 true

        // 添加跳过自定义逻辑的标签
        list.add(skipLabel);

        // 在这里插入自定义逻辑到方法开头
        methodNode.instructions.insert(list);

        // 创建返回 true 的指令列表
        InsnList returnTrueList = new InsnList();
        returnTrueList.add(returnTrueLabel);
        returnTrueList.add(new InsnNode(ICONST_1)); // 加载常量 true (1)
        returnTrueList.add(new InsnNode(IRETURN));  // 返回 true

        // 在方法结尾插入返回 true 的逻辑
        methodNode.instructions.add(returnTrueList);
    }

}
