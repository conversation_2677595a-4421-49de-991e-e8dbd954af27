package com.leave.ink.features.hud;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.external.ui.ExternalUI;
import com.leave.ink.events.hud.EventSkiaProcess;
import com.leave.ink.features.hud.dynamicIsland.DynamicIsland;
import com.leave.ink.features.hud.elements.ModuleListHud;
import com.leave.ink.features.hud.elements.TitleHud;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.features.hud.main.NotificationHud;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.SettingManager;
import com.leave.ink.features.setting.settings.*;
import com.leave.ink.language.Language;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.Utils;
import lombok.Getter;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Getter
public class HudManager implements IMinecraft {
    private final List<AbsHudElement> hudElements = new ArrayList<>();
    private static DesignGuiScreen guiScreen = null;
    public static DynamicIsland dynamicIsland =null;

    public HudManager() {
        dynamicIsland = new DynamicIsland();
    }

    public void init() {
        addElement(AuraSyncHud.class);
        addElement(ElementsHud.class);
        addElement(NotificationHud.class);
        addElement(ModuleListHud.class);
        addElement(TitleHud.class);
        EventManager.register(this);
    }

    public static void displayGui() {
        if(guiScreen == null) guiScreen = new DesignGuiScreen(Component.literal("Des"));
        mc.execute(() ->{
            mc.setScreen(guiScreen);
        });
    }

    public AbsHudElement addElement(Class<? extends AbsHudElement> clazz) {
        try {
            AbsHudElement absHudElement = clazz.getDeclaredConstructor().newInstance();

            hudElements.add(absHudElement);

            registerNative(absHudElement);
            return absHudElement;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void removeElement(String name) {
        Iterator<AbsHudElement> hudElementIterator = hudElements.iterator();
        while (hudElementIterator.hasNext()) {
            AbsHudElement absHudElement = hudElementIterator.next();
            if (absHudElement.getElementName().equals(name)) {
                SettingManager.removeObj(absHudElement);
                ExternalUI.UI_RemoveSettings(name);
        
                hudElementIterator.remove();
                ExternalUI.UI_RemoveHud(name);
                break;
            }
        }

    }
    private void registerNative(AbsHudElement absHudElement) {
        String name = absHudElement.getElementName();
        ExternalUI.execute(() -> {
            ExternalUI.UI_RegisterHud(name);
            for (Setting<?> setting : SettingManager.getSettings(absHudElement)) {
                if (setting instanceof BooleanSetting) {
                    ExternalUI.UI_RegisterBooleanSetting(name, setting.getName(), setting.getName(Language.Chinese), ((BooleanSetting) setting).getValue());
                } else if (setting instanceof NumberSetting) {
                    String str = "%." + ((NumberSetting) setting).getPrecisePattern().chars().filter(t -> t == '0').count() + "f";
                    ExternalUI.UI_RegisterNumberSetting(name, setting.getName(), setting.getName(Language.Chinese), ((NumberSetting) setting).getValue().floatValue(), (float) ((NumberSetting) setting).getMin(), (float) ((NumberSetting) setting).getMax(), ((NumberSetting) setting).getPrecisePattern().equals("#") ? "%.0f" : str);
                } else if (setting instanceof ModeSetting) {
                    ExternalUI.UI_RegisterModeSetting(name, setting.getName(), setting.getName(Language.Chinese), ((ModeSetting) setting).getValue(), ((ModeSetting) setting).getModes());
                } else if (setting instanceof ColorSetting) {
                    ExternalUI.UI_RegisterColorSetting(name, setting.getName(), setting.getName(Language.Chinese), ((ColorSetting) setting).getValue());
                }else if (setting instanceof ButtonSetting) {
                    ExternalUI.UI_RegisterButtonSetting(name, setting.getName(),setting.getName(Language.Chinese));
                }
            }
        });
    }

    public void clear() {
        Iterator<AbsHudElement> iterator = getHudElements().iterator();

        while (iterator.hasNext()) {
            AbsHudElement hudElement = iterator.next();
            if (hudElement.isMainElement()) {
                continue;
            }

            SettingManager.removeObj(hudElement);
            ExternalUI.UI_RemoveSettings(hudElement.getElementName());
            ExternalUI.UI_RemoveHud(hudElement.getElementName());
            iterator.remove();

        }
    }

    public AbsHudElement getHudElement(String name) {
        for (AbsHudElement hudElement : hudElements) {
            if (hudElement.getElementName().equals(name)) {
                return hudElement;
            }
        }
        return null;
    }

    @EventTarget
    public void onSkia(EventSkiaProcess event) {
        if(Utils.isNull()) return;
        for (AbsHudElement hudElement : hudElements) {
            hudElement.onDrawing2D(event);
        }
    }
}
