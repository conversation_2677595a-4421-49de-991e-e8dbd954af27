package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventWorld;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.fonts.FontRenderers;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.engine.Render3DEngine;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.network.chat.Component;
import net.minecraft.world.level.block.entity.*;
import net.minecraft.world.phys.AABB;
import org.lwjgl.opengl.GL11;
import java.awt.*;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@ModuleInfo(name = {
        @Text(label = "BoxESP", language = Language.English),
        @Text(label = "箱子边框", language = Language.Chinese)
}, category = Category.Render)
public class BoxESP extends Module {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Line", Arrays.asList("Line", "Fill"));
    @SettingInfo(name = {
            @Text(label = "Color", language = Language.English),
            @Text(label = "颜色", language = Language.Chinese)
    })
    private final ColorSetting colorValue = new ColorSetting(new Color(255, 255, 255, 120));
    @SettingInfo(name = {
            @Text(label = "OpenedColor", language = Language.English),
            @Text(label = "打开箱子颜色", language = Language.Chinese)
    })
    private final ColorSetting openedColorValue = new ColorSetting(new Color(0, 255, 0, 120));
    @SettingInfo(name = {
            @Text(label = "Furnace", language = Language.English),
            @Text(label = "熔炉", language = Language.Chinese)
    })
    private final BooleanSetting furnace = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "BlastFurnace", language = Language.English),
            @Text(label = "高炉", language = Language.Chinese)
    })
    private final BooleanSetting blastFurnace = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Tag", language = Language.English),
            @Text(label = "标签", language = Language.Chinese)
    })
    private final BooleanSetting tag = new BooleanSetting(false);

    private final Set<BlockEntity> openedChests = new HashSet<>();

    public BoxESP() {
        registerSetting(mode, colorValue, openedColorValue, furnace, blastFurnace, tag);
    }

    @Override
    protected void onDisable() {
        openedChests.clear();
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        openedChests.clear();
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        for (BlockEntity blockEntity : BlockUtils.getBlockEntities()) {
            onBox(event, blockEntity);

            if (blockEntity instanceof EnderChestBlockEntity enderChest) {
                AABB enderChestBox = new AABB(
                        enderChest.getBlockPos().getX() + 0.06,
                        enderChest.getBlockPos().getY(),
                        enderChest.getBlockPos().getZ() + 0.06,
                        (enderChest.getBlockPos().getX() + 0.94),
                        (enderChest.getBlockPos().getY() - 0.125 + 1),
                        (enderChest.getBlockPos().getZ() + 0.94)
                );

                boolean isOpened = enderChest.getOpenNess(event.getPartialTicks()) > 0.0f;
                if (isOpened) {
                    if (mode.getValue().equals("Fill"))
                        Render3DEngine.drawFilledBox(event.getPoseStack(), enderChestBox, openedColorValue.getValue());
                    else
                        RenderUtils.drawLineBox(event.getPoseStack(), enderChestBox, openedColorValue.getValue());
                }

                if (tag.getValue())
                    renderNames(event, blockEntity);
            }
        }
    }

    public void onBox(EventRender3D event, BlockEntity blockEntity) {
        if (blockEntity instanceof ChestBlockEntity chestBlock) {
            AABB chestBox = new AABB(
                    chestBlock.getBlockPos().getX() + 0.06,
                    chestBlock.getBlockPos().getY(),
                    chestBlock.getBlockPos().getZ() + 0.06,
                    (chestBlock.getBlockPos().getX() + 0.94),
                    (chestBlock.getBlockPos().getY() - 0.125 + 1),
                    (chestBlock.getBlockPos().getZ() + 0.94)
            );

            boolean isOpened = chestBlock.getOpenNess(event.getPartialTicks()) > 0.0f;
            if (isOpened) {
                openedChests.add(chestBlock);
            }

            Color renderColor = isOpened || openedChests.contains(chestBlock) ? openedColorValue.getValue() : colorValue.getValue();
            if (mode.getValue().equals("Fill"))
                Render3DEngine.drawFilledBox(event.getPoseStack(), chestBox, renderColor);
            else
                RenderUtils.drawLineBox(event.getPoseStack(), chestBox, renderColor);

            if (tag.getValue())
                renderNames(event, blockEntity);

        } else if ((furnace.getValue() && blockEntity instanceof FurnaceBlockEntity) || (blastFurnace.getValue() && blockEntity instanceof BlastFurnaceBlockEntity)) {
            AABB chestBox = new AABB(
                    blockEntity.getBlockPos().getX() + 0.06,
                    blockEntity.getBlockPos().getY(),
                    blockEntity.getBlockPos().getZ() + 0.06,
                    (blockEntity.getBlockPos().getX() + 0.94),
                    (blockEntity.getBlockPos().getY() - 0.125 + 1),
                    (blockEntity.getBlockPos().getZ() + 0.94)
            );

            Color renderColor = colorValue.getValue();

            if (mode.getValue().equals("Fill"))
                Render3DEngine.drawFilledBox(event.getPoseStack(), chestBox, renderColor);
            else
                RenderUtils.drawLineBox(event.getPoseStack(), chestBox, renderColor);

            if (tag.getValue())
                renderNames(event, blockEntity);
        }
    }

    private void renderNames(EventRender3D event, BlockEntity blockEntity) {
        boolean depthEnabled = GL11.glIsEnabled(GL11.GL_DEPTH_TEST);
        int depthFunc = GL11.glGetInteger(GL11.GL_DEPTH_FUNC);

        try {
            GL11.glDisable(GL11.GL_DEPTH_TEST);
            RenderSystem.depthMask(false);
            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            renderItemName(event, blockEntity);
            RenderSystem.disableBlend();
            RenderSystem.depthMask(true);
            if (depthEnabled) {
                GL11.glEnable(GL11.GL_DEPTH_TEST);
                GL11.glDepthFunc(depthFunc);
            }
        } catch (Exception ignored) {
            RenderSystem.disableBlend();
            RenderSystem.depthMask(true);
            if (depthEnabled) {
                GL11.glEnable(GL11.GL_DEPTH_TEST);
                GL11.glDepthFunc(depthFunc);
            }
        }
    }

    private void renderItemName(EventRender3D event, BlockEntity blockEntity) {
        try {
            Component nameComponent = blockEntity.getBlockState().getBlock().getName();
            double x = blockEntity.getBlockPos().getX() + 0.5;
            double y = blockEntity.getBlockPos().getY() + 1.25;
            double z = blockEntity.getBlockPos().getZ() + 0.5;
            float distanceSq = (float) mc.player.distanceToSqr(x, y, z);
            PoseStack matrixStack = event.getPoseStack();
            matrixStack.pushPose();
            matrixStack.translate(x - mc.gameRenderer.getMainCamera().getPosition().x,
                    y - mc.gameRenderer.getMainCamera().getPosition().y,
                    z - mc.gameRenderer.getMainCamera().getPosition().z);
            matrixStack.mulPose(mc.gameRenderer.getMainCamera().rotation());

            float scale = 0.025F;
            if (distanceSq > 100.0f) {
                scale = 0.025F * (float) (Math.sqrt(distanceSq * 0.01F));
            }
            matrixStack.scale(-scale, -scale, scale);
            float nameX = -FontRenderers.harmony18.getStringWidth(nameComponent.getString()) / 2.0f;
            float nameY = 0;
            FontRenderers.harmony18.drawString(matrixStack, nameComponent.getString(), nameX, nameY, colorValue.getValue().getRGB());
            matrixStack.popPose();
        } catch (Exception ignored) {
        }
    }
}
