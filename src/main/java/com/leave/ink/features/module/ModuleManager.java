package com.leave.ink.features.module;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.features.module.modules.combat.*;
import com.leave.ink.features.module.modules.movement.*;
import com.leave.ink.features.module.modules.other.*;
import com.leave.ink.features.module.modules.render.*;
import com.leave.ink.features.module.modules.settings.ClientSetting;
import com.leave.ink.features.module.modules.settings.DesignHud;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.module.modules.world.*;
import com.leave.ink.features.module.modules.world.Timer;
import net.minecraft.world.item.Item;

import java.util.*;

public class ModuleManager {
    private final List<Module> modules = new ArrayList<>();
    private final Map<String, Module> modulesMap = new HashMap<>();


    public ModuleManager() {

        register(
                new Cape(),
                new NoWeb(),
                new LongJump(),
                new AntiBlind(),
                new FastBreak(),
                new Sprint(),
                new AimAssist(),
                new AutoClicker(), new Fly(),
                new Timer(), new Reach(), new Animations(),
                new Targets(), new GodBridge(), new Disabler(),
                new SuperKnockBack(),
                new AntiFireBall(),
                new ClientSetting(),
                new KillAura(),
                new InvMove(),
                new AimTop(),
                new Ambience(),
                new InfiniteAura(),
                new Blink(),
                new Velocity(),
                new AutoArmor(),
                new AutoPot(),
                new BreakCrystal(),
                new Stuck(),
                new Eagle(),
                new Speed(),
                new ChestAura(),
                new AutoTool(),
                new FreeCam(),
                new ChestStealer(),
                new Scaffold(),
                new InvClear(),
                new AttackEffect(),
                new AntiBot(),
                new Trajectories(),
                new SkyParticles(),
                new TargetStrafe(),
                new KeepRange(),
                new HitBubbles(),
                new KillerCheck(),
                new Penis(),
                new Fucker(),
                new FullBright(),
                new ESP(),
                new Chams(),
                new NoSlow(),
                new AutoSoup(),
                new BoxESP(),
                new NameProtect(),
                new NoJumpDelay(),
                new FastPlace(),
                new Gapple(),
                new CobWebPlace(),
                new BackTrack(),
                new ItemPhysic(),
                new Criticals(),
                new NoHurtCam(),
                new Projectiles(),
                new FakeLag(),
                new WTap(),
                new RadarArrows(),
                new AutoHead(),
                new DesignHud(),
                new XRay(),
                new XrayDiamond(),
                new ItemESP(),
                new CrystalAura(),
                new BetterCamera(),
                new Scaffold2(),
                new NoFog(),
                new StrongholdDetector(),
                new ClayGuns(),
                new NoFall(),
                new NameTags()
        );

        EventManager.register(this);
    }

    public void register(Module... modules) {
        this.modules.addAll(Arrays.asList(modules));
        for (Module module : modules) {
            modulesMap.put(module.getNameKey().toLowerCase(), module);
        }
    }

    public List<Module> getModules() {
        return modules;
    }

    public ArrayList<Module> getModulesByCategory(String categoryName) {
        ArrayList<Module> list = new ArrayList<>();
        for (Module mod : getModules()) {
            if (mod.getCategory().getName().equals(categoryName)) list.add(mod);
        }
        return list;
    }

    public Module getModule(String key) {
        return modulesMap.get(key.toLowerCase());
    }
}
