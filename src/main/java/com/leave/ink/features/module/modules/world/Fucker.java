package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.rotation.MovementFix;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.protocol.game.ServerboundPlayerActionPacket;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.entity.BedBlockEntity;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import java.util.*;

@ModuleInfo(name = {
        @Text(label = "BlockBreaker", language = Language.English),
        @Text(label = "破坏方块", language = Language.Chinese)
}, category = Category.World)
public class Fucker extends Module {
    @SettingInfo(name = {
            @Text(label = "Block", language = Language.English),
            @Text(label = "方块", language = Language.Chinese)
    })
    private final ModeSetting blockValue = new ModeSetting("Bed", Arrays.asList("Bed", "EndStone", "Chest"));
    @SettingInfo(name = {
            @Text(label = "RayCast", language = Language.English),
            @Text(label = "光线对准", language = Language.Chinese)
    })
    private final BooleanSetting rayCast = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Reach", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    public final NumberSetting rangeValue = new NumberSetting(4.5, 1.0, 8.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "SilentRotation", language = Language.English),
            @Text(label = "静态转头", language = Language.Chinese)
    })
    private final BooleanSetting silentRotationValue = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Grim", language = Language.English),
            @Text(label = "绕过GrimAC", language = Language.Chinese)
    })
    private final BooleanSetting grim = new BooleanSetting(false);

    public BlockPos pos;
    private BlockPos oldPos;
    private int blockHitDelay = 0;
    private float currentDamage = 0F;

    public Fucker() {
        registerSetting(blockValue, rayCast, rangeValue, silentRotationValue, grim);
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
        if (killAura.isEnable() && killAura.target != null) {
            return;
        }

        Block block = blockValue.getValue().equals("Bed") ? isBed() : blockValue.getValue().equals("EndStone") ? Blocks.END_STONE : blockValue.getValue().equals("Chest") ? Blocks.CHEST : null;
        if (block == null)
            return;

        if (blockValue.getValue().equals("Bed")) {
            BlockPos bedPos = find(block);
            if (bedPos != null && isSurroundedByBlocks(bedPos) && grim.getValue()) {
                pos = findNearbyBlock(bedPos);
            } else {
                pos = bedPos;
            }
        } else {
            if (pos == null || BlockUtils.getBlock(pos) != block || BlockUtils.getCenterDistance(pos) > rangeValue.getValue().doubleValue()) {
                pos = find(block);
            }
        }

        if (pos == null) {
            currentDamage = 0F;
            return;
        }

        BlockPos currentPos = pos;
        Rotation rotations = RotationUtils.getRotationBlock(currentPos);

        if (oldPos != null && !oldPos.equals(currentPos)) {
            currentDamage = 0F;
        }

        oldPos = currentPos;

        if (blockHitDelay > 0) {
            blockHitDelay--;
            return;
        }

        if (silentRotationValue.getValue()) {
            RotationUtils.setRotation(rotations, 10, MovementFix.NORMAL);
        } else {
            rotations.toPlayer(mc.player);
        }

        if (rayCast.getValue() && !RotationUtils.isLookingAtBlock(RotationUtils.serverRotation, currentPos, rangeValue.getValue().floatValue() + 1, 0.9F)) {
            return;
        }

        BlockState blockState = mc.level.getBlockState(currentPos);
        Direction direction = RotationUtils.getViewDirection(rotations, currentPos);

        if (currentDamage == 0F) {
            mc.getConnection().send(new ServerboundPlayerActionPacket(ServerboundPlayerActionPacket.Action.ABORT_DESTROY_BLOCK, currentPos, direction));
            mc.getConnection().send(new ServerboundPlayerActionPacket(ServerboundPlayerActionPacket.Action.START_DESTROY_BLOCK, currentPos, direction));
            blockState.attack(mc.level, currentPos, mc.player);
        }

        mc.player.swing(InteractionHand.MAIN_HAND);

        currentDamage += blockState.getDestroyProgress(mc.player, mc.level, currentPos);
        mc.level.destroyBlockProgress(mc.player.getId(), currentPos, (int) (currentDamage * 10F));
        mc.getTutorial().onDestroyBlock(mc.level, currentPos, blockState, Mth.clamp(currentDamage, 0.0F, 1.0F));

        if (currentDamage >= 1F) {
            mc.getConnection().send(new ServerboundPlayerActionPacket(ServerboundPlayerActionPacket.Action.STOP_DESTROY_BLOCK, currentPos, direction));
            mc.level.destroyBlockProgress(mc.player.getId(), currentPos, -1);
            blockHitDelay = 4;
            currentDamage = 0F;
            pos = null;
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (grim.getValue() && event.getPacket() instanceof ServerboundPlayerActionPacket actionPacket) {
            if (actionPacket.getAction() == ServerboundPlayerActionPacket.Action.STOP_DESTROY_BLOCK) {
                mc.getConnection().send(
                        new ServerboundPlayerActionPacket(
                                ServerboundPlayerActionPacket.Action.ABORT_DESTROY_BLOCK,
                                actionPacket.getPos(),
                                actionPacket.getDirection()
                        )
                );
                mc.level.setBlock(actionPacket.getPos(), Blocks.AIR.defaultBlockState(), 3);
            }
        }
    }

    private BlockPos find(Block block) {
        return BlockUtils.searchBlocks(rangeValue.getValue().intValue() + 1).entrySet().stream()
                .filter(entry -> {
                    BlockState blockState = entry.getValue().defaultBlockState();
                    return blockState.getBlock() == block
                            && BlockUtils.getCenterDistance(entry.getKey()) <= rangeValue.getValue().doubleValue();
                })
                .min(Comparator.comparingDouble(entry -> BlockUtils.getCenterDistance(entry.getKey())))
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    public static List<BlockPos> getSurroundingBlocks(BlockPos pos) {
        List<BlockPos> surroundingBlocks = new ArrayList<>();
        surroundingBlocks.add(pos.above());
        surroundingBlocks.add(pos.below());
        surroundingBlocks.add(pos.north());
        surroundingBlocks.add(pos.south());
        surroundingBlocks.add(pos.east());
        surroundingBlocks.add(pos.west());
        return surroundingBlocks;
    }

    private BlockPos findNearbyBlock(BlockPos bedPos) {
        return getSurroundingBlocks(bedPos).stream()
                .filter(pos -> !mc.level.getBlockState(pos).isAir())
                .filter(pos -> mc.level.getBlockState(pos).getDestroySpeed(mc.level, pos) >= 0)
                .findFirst()
                .orElse(null);
    }

    private boolean isSurroundedByBlocks(BlockPos bedPos) {
        return getSurroundingBlocks(bedPos).stream()
                .noneMatch(pos -> mc.level.getBlockState(pos).isAir());
    }

    private Block isBed() {
        for (BlockEntity blockEntity : BlockUtils.getBlockEntities()) {
            if (blockEntity instanceof BedBlockEntity bedBlock) {
                return bedBlock.getBlockState().getBlock();
            }
        }

        return null;
    }
}
