package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.darkmagician6.eventapi.types.Priority;
import com.leave.ink.events.*;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.network.PacketUtils;
import com.leave.ink.utils.timer.TimeUtils;
import com.leave.ink.utils.wrapper.WrapperUtils;
import com.leave.ink.features.module.Category;
import com.leave.ink.utils.player.MovementUtils;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.misc.MathUtils;
import com.leave.ink.utils.player.PlayerUtils;
import com.leave.ink.utils.rotation.MovementFix;
import com.leave.ink.utils.rotation.RayCastUtil;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import com.leave.ink.features.module.modules.world.AntiBot;
import lombok.Getter;
import net.minecraft.client.gui.screens.ProgressScreen;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.block.*;
import net.minecraft.world.phys.*;
import org.lwjgl.glfw.GLFW;
import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;

@ModuleInfo(name = {
        @Text(label = "Velocity", language = Language.English),
        @Text(label = "反击退", language = Language.Chinese)
}, category = Category.Combat)
public class Velocity extends Module {

    @SettingInfo(name = {
            @Text(label = "Ticks", language = Language.English),
            @Text(label = "延迟时刻", language = Language.Chinese)
    })
    private final NumberSetting ticks = new NumberSetting(10, 1, 15, "#.#");

    @SettingInfo(name = {
            @Text(label = "SmartPing", language = Language.English),
            @Text(label = "智能Ping", language = Language.Chinese)
    })
    private final BooleanSetting smartPing = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Grim Full Auto Jump", language = Language.English),
            @Text(label = "严峻反作弊全反自动跳跃", language = Language.Chinese)
    })
    private final BooleanSetting grimFullAutoJump = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Grim Full Skip ticks", language = Language.English),
            @Text(label = "严峻反作弊全反跳过时刻值", language = Language.Chinese)
    })
    private final NumberSetting grimFullSkipTicks = new NumberSetting(3, 1, 4, "#.#");

    @SettingInfo(name = {
            @Text(label = "Grim Full Send UsePacket Ticks", language = Language.English),
            @Text(label = "严峻反作弊全反发送使用包时刻值", language = Language.Chinese)
    })
    private final NumberSetting grimFullSendC08Ticks = new NumberSetting(3, 1, 4, "#.#");

    @SettingInfo(name = {
            @Text(label = "VulCan C0F Compatible", language = Language.English),
            @Text(label = "VulCanC0F兼容", language = Language.Chinese)
    })
    private final BooleanSetting vulCanC0FCompatible = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Grim Full Timeout", language = Language.English),
            @Text(label = "卡顿超时自动停止", language = Language.Chinese)
    })
    private final BooleanSetting timeout = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Timeout", language = Language.English),
            @Text(label = "超时时间", language = Language.Chinese)
    })
    private final NumberSetting timeoutTick = new NumberSetting(2000, 50, 10000, "#");

    @SettingInfo(name = {
            @Text(label = "Attacked FlightObject", language = Language.English),
            @Text(label = "攻击飞行物", language = Language.Chinese)
    })
    private final BooleanSetting attacked_FlightObject = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Target Motion", language = Language.English),
            @Text(label = "击退量", language = Language.Chinese)
    })
    private final NumberSetting targetMotion = new NumberSetting(0.1, 0.01, 1, "#.00");

    @SettingInfo(name = {
            @Text(label = "Counter", language = Language.English),
            @Text(label = "攻击次数", language = Language.Chinese)
    })
    private final NumberSetting counter = new NumberSetting(1, 1, 10, "#");

    @SettingInfo(name = {
            @Text(label = "Ray Cast", language = Language.English),
            @Text(label = "射线检测", language = Language.Chinese)
    })
    private final BooleanSetting rayCast = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Sprint Only", language = Language.English),
            @Text(label = "仅疾跑生效", language = Language.Chinese)
    })
    private final BooleanSetting sprintOnly = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Only Move", language = Language.English),
            @Text(label = "仅在移动生效", language = Language.Chinese)
    })
    private final BooleanSetting onlyMove = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Only Ground", language = Language.English),
            @Text(label = "仅在地板生效", language = Language.Chinese)
    })
    private final BooleanSetting onlyGround = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Reduce Amount", language = Language.English),
            @Text(label = "减少击退量", language = Language.Chinese)
    })
    private final NumberSetting reduceAmount = new NumberSetting(0.8, 0.3, 1, "#.0");

    @SettingInfo(name = {
            @Text(label = "Flag Check", language = Language.English),
            @Text(label = "回弹检测", language = Language.Chinese)
    })
    private final BooleanSetting flagCheckValue = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Flag Disable", language = Language.English),
            @Text(label = "回弹关闭", language = Language.Chinese)
    })
    private final BooleanSetting flagDisable = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Flag Ticks", language = Language.English),
            @Text(label = "回弹阈值", language = Language.Chinese)
    })
    private final NumberSetting flagTickValue = new NumberSetting(6, 0, 30, "#");

    @SettingInfo(name = {
            @Text(label = "Flag Debug", language = Language.English),
            @Text(label = "回弹调试信息", language = Language.Chinese)
    })
    private final BooleanSetting flagDebugMessageValue = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Debug Message", language = Language.English),
            @Text(label = "调试信息", language = Language.Chinese)
    })
    private final BooleanSetting debugMessageValue = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Dragon Click Reduce", language = Language.English),
            @Text(label = "DC减少击退", language = Language.Chinese)
    })
    private final BooleanSetting click = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Max Click", language = Language.English),
            @Text(label = "最大攻击数", language = Language.Chinese)
    })
    private final NumberSetting maxClick = new NumberSetting(3, 1, 20, "#");

    @SettingInfo(name = {
            @Text(label = "Jump", language = Language.English),
            @Text(label = "跳跃重置", language = Language.Chinese)
    })
    private final BooleanSetting jumpReset = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Jump Percent", language = Language.English),
            @Text(label = "跳跃重置成功率", language = Language.Chinese)
    })
    private final NumberSetting percent = new NumberSetting(100, 0, 100, "#");
    @SettingInfo(name = {
            @Text(label = "JumpResetRotate", language = Language.English),
            @Text(label = "跳跃重置旋转", language = Language.Chinese)
    })
    private final BooleanSetting jumpResetRotateValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "SilentRate", language = Language.English),
            @Text(label = "静默旋转", language = Language.Chinese)
    })
    private final BooleanSetting silentRate = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Tick", Arrays.asList("Tick", "Vanilla", "Grim Attack", "Grim Full", "JumpReset", "Reduce", "Watchdog", "Legit"),
            // Legit
            new SettingAttribute<>(click, "Legit"),
            new SettingAttribute<>(maxClick, "Legit"),
            new SettingAttribute<>(jumpReset, "Legit"),
            new SettingAttribute<>(percent, "Legit"),
            //tick
            new SettingAttribute<>(ticks, "Tick"),
            //Full
            new SettingAttribute<>(smartPing, "Grim Full"),
            new SettingAttribute<>(grimFullAutoJump, "Grim Full"),
            new SettingAttribute<>(grimFullSkipTicks, "Grim Full"),
            new SettingAttribute<>(grimFullSendC08Ticks, "Grim Full"),
            new SettingAttribute<>(vulCanC0FCompatible, "Grim Full"),
            new SettingAttribute<>(timeout, "Grim Full"),
            new SettingAttribute<>(timeoutTick, "Grim Full"),
            //Attack
            new SettingAttribute<>(attacked_FlightObject, "Grim Attack"),
            new SettingAttribute<>(targetMotion, "Grim Attack"),
            new SettingAttribute<>(counter, "Grim Attack"),
            new SettingAttribute<>(rayCast, "Grim Attack"),
            new SettingAttribute<>(sprintOnly, "Grim Attack"),
            //Reduce
            new SettingAttribute<>(reduceAmount, "Reduce"),
            //JumpReset
            new SettingAttribute<>(jumpResetRotateValue, "JumpReset"),
            new SettingAttribute<>(silentRate, "JumpReset")
    );

    private final Queue<Packet<?>> packets = new LinkedList<>();
    public boolean attackedFlightObject = false;
    private boolean slowdownTicks = false;
    private boolean velocityInput = false;
    public boolean attacked = false;
    public boolean forceSprint = false;
    private double reduceXZ;

    // Grim Full
    private final LinkedBlockingDeque<Packet<ClientGamePacketListener>> inBound = new LinkedBlockingDeque<>();
    @Getter
    private boolean shouldDelay = false;
    private boolean needVelocity = false;
    @Getter
    private boolean skipping = false;
    private boolean directed = false;
    @Getter
    private boolean accept = false;
    private final TimeUtils acceptMaxTime = new TimeUtils();
    private boolean jump = false;
    private int clickCount = 0;
    private int grimAction = 0;
    private int preC0f = 0;
    private int flags;
    private int tick;
    private ClientboundSetEntityMotionPacket velocityPacket;
    public int inGameTick;
    BlockHitResult result;

    public Velocity() {
        registerSetting(mode, onlyMove, onlyGround, flagCheckValue, flagDisable, flagTickValue, flagDebugMessageValue, debugMessageValue);
    }

    @Override
    public void onDisable() {
        reset();
    }

    @Override
    public void onEnable() {
        reset();
    }

    public void reset() {
        if (Utils.isNull()) return;

        packets.clear();
        inBound.clear();
        attackedFlightObject = false;
        velocityInput = false;
        jump = false;
        shouldDelay = false;
        skipping = false;
        attacked = false;
        acceptMaxTime.reset();
        forceSprint = false;
        directed = false;
        accept = false;
        grimAction = 0;
        reduceXZ = 0;
        preC0f = 0;
        tick = 0;
        clickCount = 0;
        processPackets();

    }

    public void processPackets() {
        while (!inBound.isEmpty()) {
            try {
                inBound.poll().handle(mc.getConnection());
            } catch (Exception ignored) {
                inBound.clear();
            }
        }
    }

    @EventTarget
    public void onTick(EventTick event) {
        if (Utils.isNull()) return;

        if (mc.player.isDeadOrDying() || !mc.player.isAlive() || mc.player.getHealth() <= 0) {
            reset();
            return;
        }


        if (skipping) {
            if (!accept) {
                WrapperUtils.setSkipTicks(grimFullSkipTicks.getValue().intValue());
            } else {
                WrapperUtils.setSkipTicks(1);
                accept = false;
                skipping = false;
            }
        }
    }

    @EventTarget
    private void onMathEvent(EventMoveMath event) {
        if ((onlyGround.getValue() && !mc.player.onGround()) || (onlyMove.getValue() && !MovementUtils.isMoving()) || flags != 0)
            return;


        if (mode.is("Tick")) {
            if (needVelocity) {
                event.setCancelled(true);
                tick++;
            } else {
                event.setCancelled(false);
            }
        }

        if (tick > ticks.getValue().intValue() || !needVelocity) {
            needVelocity = false;
            tick = 0;
        }
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        reset();
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (Utils.isNull()) return;


        if (flagCheckValue.getValue()) {
            if (flags > 0) {
                flags--;
                return;
            }
        }

        if (mc.player.isDeadOrDying() || !mc.player.isAlive() || mc.player.getHealth() <= 0 || mc.screen instanceof ProgressScreen) {
            reset();
            return;
        }

        switch (mode.getValue()) {
            case "JumpReset": {
                if (mc.player.onGround() && mc.player.isSprinting() && !checks()) {
                    if (jumpResetRotateValue.getValue() && mc.player.hurtTime > 0) {
                        if (velocityPacket == null) return;
                        float motionX = (float) (velocityPacket.getXa() / 8000.0D);
                        float motionZ = (float) (velocityPacket.getZa() / 8000.0D);
                        float fixedYaw = (float) Math.toDegrees(Math.atan2(motionX, -motionZ));
                        if (!silentRate.getValue()) {
                            mc.player.setYRot(fixedYaw);
                        } else {
                            RotationUtils.setRotation(new Rotation(fixedYaw, mc.player.getXRot()), 10, MovementFix.NORMAL, false);
                        }
                    }
                    if (mc.player.hurtTime > 6 && !mc.options.keyJump.isDown()) {
                        mc.options.keyJump.setDown(true);
                    } else if (!Main.INSTANCE.moduleManager.getModule("Scaffold").isEnable()) {
                        boolean isHoldingJump = GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) == GLFW.GLFW_PRESS;
                        mc.options.keyJump.setDown(isHoldingJump);
                    }
                }
                break;
            }

            case "Grim Attack": {
                while (!packets.isEmpty()) {
                    mc.player.connection.send(packets.poll());
                }

                Vec3 deltaMovement = mc.player.getDeltaMovement();

                if (slowdownTicks) {
                    WrapperUtils.setSkipTicks(1);
                    slowdownTicks = false;
                }

                if (velocityInput) {
                    if (attacked) {
                        mc.player.setDeltaMovement(deltaMovement.x * reduceXZ, deltaMovement.y, deltaMovement.z * reduceXZ);
                        attacked = false;
                    }
                    if (attackedFlightObject) {
                        mc.player.setDeltaMovement(deltaMovement.x * reduceXZ, deltaMovement.y, deltaMovement.z * reduceXZ);
                        attackedFlightObject = false;
                    }
                    if (mc.player.hurtTime == 0) reset();
                }

                break;
            }

            case "Grim Full": {
                if (smartPing.getValue() && !accept && timeout.getValue()) {
                    if (acceptMaxTime.hasTimeElapsed(timeoutTick.getValue().longValue())) {
                        accept = true;
                    }
                }
                break;
            }

            case "Legit": {
                if (!click.getValue() || clickCount > maxClick.getValue().intValue()) {
                    forceSprint = false;
                    return;
                }
                clickCount++;

                WrapperUtils.setMissTime(0);
                PlayerUtils.sendClick(1, true);

                break;
            }
        }

        this.mode.setValue(mode.getValue());
    }

    @EventTarget
    public void onStrafe(EventStrafe event) {
        if (Utils.isNull()) return;


        if (mode.is("Grim Full") && !mode.is("Grim Attack") && grimFullAutoJump.getValue() && jump && mc.player.hurtTime > 0) {
            if (mc.player.onGround() && !mc.options.keyJump.isDown()) {
                mc.player.jumpFromGround();
                jump = false;
            }
        }

        if (mode.is("Legit")) {
            if (jumpReset.getValue() && jump && mc.player.hurtTime > 0 && checks() && Math.random() <= percent.getValue().doubleValue() / 100.0) {
                if (mc.player.onGround() && !mc.options.keyJump.isDown()) {
                    mc.player.jumpFromGround();
                    jump = false;
                    if (click.getValue()) {
                        forceSprint = true;
                        clickCount = 0;
                    }
                }
            }
        }
    }

    @EventTarget
    public void onAttack(EventAttack event) {
        if (Utils.isNull()) return;


        if (mode.is("Reduce")) {
            if (mc.player.hurtTime < 3) return;

            mc.player.setDeltaMovement(
                    mc.player.getDeltaMovement().x * reduceAmount.getValue().doubleValue(),
                    mc.player.getDeltaMovement().y,
                    mc.player.getDeltaMovement().z * reduceAmount.getValue().doubleValue());
        }
    }

    @EventTarget(Priority.HIGHEST)
    public void onHandleReceivePacket(EventHandleReceivePacket event) {
        if (Utils.isNull()) return;


        Packet<?> packet = event.getPacket();

        if (mc.player.isDeadOrDying() || !mc.player.isAlive() || mc.player.getHealth() <= 0 || mc.screen instanceof ProgressScreen) {
            reset();
            return;
        }

        if ((onlyGround.getValue() && !mc.player.onGround()) || (onlyMove.getValue() && !MovementUtils.isMoving()) || flags != 0) {
            return;
        }

        if (mode.is("Grim Full")) {
            if (packet instanceof ClientboundPingPacket wrapper && vulCanC0FCompatible.getValue()) {
                if (Math.abs(preC0f - wrapper.getId()) == 1) {
                    grimAction = wrapper.getId();
                    directed = true;
                }

                preC0f = wrapper.getId();

                if (grimAction != wrapper.getId() && Math.abs(grimAction - wrapper.getId()) > 10 && directed && mc.player.hurtTime == 10) {
                    event.setCancelled(true);
                }
            } else if (packet instanceof ClientboundSystemChatPacket wrapper) {
                String s = wrapper.content().getString();
                if (s.contains("错误") || s.contains("Unknown command.") || s.contains("/help") || s.contains("无法")) {
                    accept = true;
                    event.setCancelled(true);
                }
            }
        }
    }

    @EventTarget(Priority.HIGHEST)
    public void onSyncHandleReceivePacket(EventSyncHandleReceivePacket event) {
        KillAura aura = ((KillAura) Main.INSTANCE.moduleManager.getModule("KillAura"));
        if (Utils.isNull()) return;


        Packet<?> packet = event.getPacket();

        if (mc.player.isDeadOrDying() || !mc.player.isAlive() || mc.player.getHealth() <= 0 || mc.screen instanceof ProgressScreen) {
            reset();
            return;
        }

        if ((onlyGround.getValue() && !mc.player.onGround()) || (onlyMove.getValue() && !MovementUtils.isMoving()) || flags != 0) {
            return;
        }

        if (packet instanceof ClientboundSetEntityMotionPacket packetEntityVelocity) {
            if (packetEntityVelocity.getId() != mc.player.getId()) return;

            needVelocity = true;

            if (debugMessageValue.getValue())
                switch (mode.getValue()) {
                    case "Vanilla" -> event.setCancelled(true);

                    case "Legit" -> jump = true;

                    case "JumpReset" -> {
                        if (jumpResetRotateValue.getValue()) {
                            if (packetEntityVelocity.getYa() < 0) {
                                velocityPacket = null;
                            } else {
                                velocityPacket = packetEntityVelocity;
                            }
                        }
                    }

                    case "Grim Full" -> {
                        if (!directed) {
                            event.setCancelled(false);
                            return;
                        }
                        if (inGameTick < 60) {
                            event.setCancelled(false);
                            return;
                        }
                        if (packetEntityVelocity.getYa() < 0) {
                            event.setCancelled(false);
                            return;
                        }
                        shouldDelay = true;
                        event.setCancelled(true);
                    }

                    case "Grim Attack" -> {
                        // Entity
                        if (aura.target != null && aura.target != mc.player && !mc.player.onClimbable()) {
                            final HitResult hitResult = RayCastUtil.rayCast(new Rotation(WrapperUtils.getYRotLast(), WrapperUtils.getXRotLast()), 3.0);
                            if (rayCast.getValue() && hitResult != null) {
                                if (hitResult.getType() != HitResult.Type.ENTITY || !aura.target.equals(((EntityHitResult) hitResult).getEntity())) {
                                    return;
                                }
                            }
                            boolean state = WrapperUtils.getWasSprinting();

                            if (!sprintOnly.getValue() || state) {
                                if (attacked) return;

                                velocityInput = true;

                                reduceXZ = 1;

                                if (!state) {
                                    packets.offer(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.START_SPRINTING));
                                    packets.offer(new ServerboundMovePlayerPacket.StatusOnly(mc.player.onGround()));
                                    slowdownTicks = true;
                                }

                                final double motionX = packetEntityVelocity.getXa() / 8000.0;
                                final double motionZ = packetEntityVelocity.getZa() / 8000.0;
                                double velocityDistance = Math.sqrt(motionX * motionX + motionZ * motionZ);

                                int counter = 0;
                                while (velocityDistance * reduceXZ > targetMotion.getValue().floatValue() && counter <= this.counter.getValue().intValue()) {
                                    packets.offer(ServerboundInteractPacket.createAttackPacket(aura.target, mc.player.isShiftKeyDown()));
                                    packets.offer(new ServerboundSwingPacket(InteractionHand.MAIN_HAND));
                                    reduceXZ *= 0.6;
                                    counter++;
                                }

                                if (!state) {
                                    packets.offer(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.STOP_SPRINTING));
                                }

                                attacked = true;
                            }
                        }

                        // Projectile
                        if (attacked_FlightObject.getValue()) {
                            for (Entity entity : mc.level.entitiesForRendering()) {
                                if (entity != null
                                        && entity != mc.player
                                        && entity instanceof Projectile
                                        && !((AntiBot) Main.INSTANCE.moduleManager.getModule("AntiBot")).isBot((LivingEntity) entity)
                                        && RotationUtils.getDistanceToEntityBox(entity) > 6.0) {

                                    final HitResult hitResult = RayCastUtil.rayCast(new Rotation(WrapperUtils.getYRotLast(), WrapperUtils.getXRotLast()), 3.0);
                                    if (rayCast.getValue() && hitResult != null) {
                                        if (hitResult.getType() != HitResult.Type.ENTITY || !entity.equals(((EntityHitResult) hitResult).getEntity())) {
                                            return;
                                        }
                                    }
                                    if (attackedFlightObject) return;

                                    if (entity.onGround()) continue;

                                    velocityInput = true;

                                    boolean state = WrapperUtils.getWasSprinting();

                                    reduceXZ = 1;

                                    if (!state) {
                                        packets.offer(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.START_SPRINTING));
                                        packets.offer(new ServerboundMovePlayerPacket.StatusOnly(mc.player.onGround()));
                                        slowdownTicks = true;
                                    }

                                    final double motionX = packetEntityVelocity.getXa() / 8000.0;
                                    final double motionZ = packetEntityVelocity.getZa() / 8000.0;
                                    double velocityDistance = Math.sqrt(motionX * motionX + motionZ * motionZ);

                                    int counter = 0;
                                    while (velocityDistance * reduceXZ > targetMotion.getValue().floatValue() && counter <= this.counter.getValue().intValue()) {
                                        packets.offer(ServerboundInteractPacket.createAttackPacket(entity, mc.player.isShiftKeyDown()));
                                        packets.offer(new ServerboundSwingPacket(InteractionHand.MAIN_HAND));
                                        reduceXZ *= 0.6;
                                        counter++;
                                    }

                                    if (!state) {
                                        packets.offer(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.STOP_SPRINTING));
                                    }

                                    attackedFlightObject = true;
                                    break;
                                }
                            }
                        }
                    }

                    case "Watchdog" -> {
                        event.setCancelled(true);
                        mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, packetEntityVelocity.getYa() / 8000.0D, mc.player.getDeltaMovement().z);
                    }
                }
        }

        if (packet instanceof ClientboundExplodePacket wrapper) {
            if (wrapper.getKnockbackX() == 0.0F || wrapper.getKnockbackY() == 0.0F || wrapper.getKnockbackZ() == 0.0F) {
                return;
            }

            if (debugMessageValue.getValue()) {
                mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal("[Debug]Explode Entity Velocity >> " + MathUtils.getRandomNumber(1000, 10000) + " <<"));
            }

            switch (mode.getValue()) {
                case "Vanilla" -> event.setCancelled(true);

                case "Grim Full" -> {
                    if (!directed) {
                        event.setCancelled(false);
                        return;
                    }
                    if (inGameTick < 60) {
                        event.setCancelled(false);
                        return;
                    }
                    if (wrapper.getKnockbackY() < 0) {
                        event.setCancelled(false);
                        return;
                    }
                    shouldDelay = true;
                    event.setCancelled(true);
                    jump = true;
                }
            }
        }

        if (packet instanceof ClientboundPlayerPositionPacket wrapper && mc.player.tickCount > 120) {
            if (wrapper.getId() != mc.player.getId()) return;

            if (mode.is("Legit")) {
                forceSprint = false;
            }

            if (flagCheckValue.getValue()) {
                flags = flagTickValue.getValue().intValue();

                if (flagDebugMessageValue.getValue()) {
                    mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal("[Velocity]Debug Flags."));
                }
            }

            if (flagDisable.getValue()) {
                toggle();
                if (flagDebugMessageValue.getValue()) {
                    mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal("[Velocity]Auto Disabled."));
                }
            }
        }

        if (packet instanceof ClientboundLoginPacket) {
            reset();
        }

        var autoSoup = (AutoSoup) Main.INSTANCE.moduleManager.getModule("AutoSoup");

        if (autoSoup.isEnable() && autoSoup.onEat) {
            shouldDelay = false;
            return;
        }

        if (shouldDelay && inGameTick >= 60) {

            if (mode.getValue().equals("Grim Full")) {
                if (!(packet instanceof ClientboundSystemChatPacket)
                        && !(packet instanceof ClientboundSetEntityMotionPacket)
                        && !(packet instanceof ClientboundExplodePacket)
                        && !(packet instanceof ClientboundSetTimePacket)
                        && !(packet instanceof ClientboundMoveEntityPacket)
                        && !(packet instanceof ClientboundTeleportEntityPacket)
                        && !(packet instanceof ClientboundSoundPacket)
                        && !(packet instanceof ClientboundSetHealthPacket)
                        && !(packet instanceof ClientboundPlayerPositionPacket)) {
                    event.setCancelled(true);
                    inBound.add((Packet<ClientGamePacketListener>) packet);

                    if (packet instanceof ClientboundPingPacket) {
                        BlockHitResult blockRayTraceResult = (BlockHitResult) PlayerUtils.pickCustom(4.5D, mc.player.getYRot(), 90F);

                        if (blockRayTraceResult == null) return;

                        if (BlockUtils.isAirBlock(blockRayTraceResult.getBlockPos())) return;

                        AABB aabb = new AABB(blockRayTraceResult.getBlockPos().above());

                        if (!mc.player.getBoundingBox().intersects(aabb)) return;

                        while (!inBound.isEmpty()) {
                            try {
                                inBound.poll().handle(mc.getConnection());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        result = new BlockHitResult(blockRayTraceResult.getLocation(), blockRayTraceResult.getDirection(), blockRayTraceResult.getBlockPos(), false);

                        float yaw = WrapperUtils.getYRotLast() - (float) MathUtils.getRandomInRange(0.002F, 0.004F);
                        float pitch = 90F;
                        WrapperUtils.setYRotLast(yaw);
                        WrapperUtils.setXRotLast(pitch);
                        WrapperUtils.setPositionReminder(20);
                        mc.hitResult = result;
                        PacketUtils.sendPacket(new ServerboundMovePlayerPacket.Rot(yaw, pitch, mc.player.onGround()));
                        RotationUtils.setRotation(new Rotation(yaw, pitch), 10, MovementFix.NORMAL);
                        mc.gameMode.useItemOn(mc.player, InteractionHand.MAIN_HAND, result);
                        PacketUtils.sendPacketNoEvent(new ServerboundPongPacket(0));
                        if (smartPing.getValue()) {
                            mc.getConnection().sendCommand("/i am zh#1337-114514@");
                            accept = false;
                            skipping = true;
                        } else {
                            WrapperUtils.setSkipTicks(grimFullSkipTicks.getValue().intValue());
                        }
                        jump = true;
                        result = null;
                        shouldDelay = false;
                    }
                }
            }
        }
    }

    private boolean checks() {
        return (mc.level.getBlockState(mc.player.blockPosition()).getBlock() instanceof WebBlock && mc.player.onGround()) || mc.player.isInLava() || mc.player.isOnFire() || mc.player.isInWater();
    }

    @Override
    public String getTag() {
        return mode.getValue();
    }
}

