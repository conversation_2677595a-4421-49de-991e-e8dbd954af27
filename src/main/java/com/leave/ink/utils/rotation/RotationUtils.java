package com.leave.ink.utils.rotation;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.darkmagician6.eventapi.types.Priority;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.features.module.modules.movement.TargetStrafe;
import com.leave.ink.features.module.modules.other.Disabler;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.misc.MathUtils;
import com.leave.ink.utils.render.engine.Render3DEngine;
import com.leave.ink.utils.rotation.vector.Vector3d;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.wrapper.WrapperUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.PlayerModel;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.Vec3i;
import net.minecraft.network.protocol.game.ServerboundMovePlayerPacket;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.*;
import java.util.HashMap;
import java.util.Map;

@SuppressWarnings("all")
public class RotationUtils implements IMinecraft {
    public static Rotation rotations = null;
    public static Rotation targetRotations = null;
    public static Rotation lastRotations = null;

    public static Rotation lastServerRotations = null;
    public static boolean active = false;
    private static boolean smoothed = false;
    private static double rotationSpeed;
    public static boolean randomRotation = false;
    public static MovementFix correctMovement = null;
    public static Rotation serverRotation = new Rotation(0f, 0f);
    public static final float PI = (float) Math.PI;
    public static final float TO_DEGREES = 180.0f / PI;

    public RotationUtils() {
        EventManager.register(this);

    }

    @EventTarget(Priority.LOWEST)
    public void onPitchRender(PitchRenderEvent event) {
        if (rotations != null && active) {
            event.setPitch(rotations.getPitch());
        }
    }

    @EventTarget
    public void onReset(EventLocalPlayerTick eventLocalPlayerTick) {


    }

    @EventTarget(Priority.LOWEST)
    public void onUpdate(EventUpdate event) {
        if (rotations != null) {
            if (Math.abs(((rotations.getYaw() - clampAngle(mc.player.getYRot())) % 360)) < 1 &&
                    Math.abs((rotations.getPitch() - mc.player.getXRot())) < 1) {
                active = false;
            }
        }

        if (!active || rotations == null || lastRotations == null || targetRotations == null || lastServerRotations == null) {
            lastServerRotations = new Rotation(mc.player.getYRot(), mc.player.getXRot());
            targetRotations = lastServerRotations;
            lastRotations = targetRotations;
            rotations = lastRotations;
            randomRotation = false;
        }

        if (correctMovement == MovementFix.BACKWARDS_SPRINT && active) {
            if (Math.abs(rotations.getYaw() - Math.toDegrees(direction())) > 45) {
                mc.options.keySprint.setDown(false);
                mc.player.setSprinting(false);
            }
        }

        if (active) {
            smooth();
        }

        //mc.player.setYRot(mc.player.getYRot() + 0.001f);
    }

    @EventTarget
    public void onUseItem(EventUseItem eventUseItem) {
        if (active && (correctMovement == MovementFix.NORMAL || correctMovement == MovementFix.TRADITIONAL) && rotations != null) {
            eventUseItem.setYaw(rotations.getYaw());
            eventUseItem.setPitch(rotations.getPitch());
        }

    }

    @EventTarget
    public void onAnimation(EventAnimationSetup eventAnimationSetup) {
        PlayerModel<?> playerModel = eventAnimationSetup.getPlayerModel();
        if (targetRotations != null) {
            // playerModel.head.xRot = (float) ((double) RotationUtils.targetRotations.getPitch() / 57.29577951308232d);

        }
        if (active && rotations != null) {

            playerModel.head.xRot = (float) ((double) RotationUtils.rotations.getPitch() / 57.29577951308232d);

        }
    }

    @EventTarget(0)
    public void onMoveIn(EventMoveInput event) {
        //silent move
        TargetStrafe targetStrafe = (TargetStrafe) Main.INSTANCE.moduleManager.getModule("TargetStrafe");
        if (!targetStrafe.active && targetStrafe.target == null) {
            if (active && correctMovement == MovementFix.NORMAL && rotations != null) {
                MoveUtils.fixMovement(event, rotations.getYaw());
            }
        }
    }

    @EventTarget(1)
    public void onStrafe(EventStrafe event) {
        if (active && (correctMovement == MovementFix.NORMAL || correctMovement == MovementFix.TRADITIONAL) && rotations != null) {
            event.setYaw(rotations.getYaw());
        }
    }

    @EventTarget(1)
    public void onJump(EventJump event) {
        if (active && (correctMovement == MovementFix.NORMAL || correctMovement == MovementFix.TRADITIONAL || correctMovement == MovementFix.BACKWARDS_SPRINT) && rotations != null) {
            event.setYaw(rotations.getYaw());
        }
    }

    @EventTarget(Priority.HIGHEST)
    public void onPacket(EventPacket event) {
        if (event.getPacketType() == EventPacket.PacketType.Client) {
            if (event.getPacket() instanceof ServerboundMovePlayerPacket packet && Disabler.instance.isEnable() && Disabler.instance.grim.getValue() && Disabler.instance.aim360.getValue()) {
                if (!event.isCancelled()) {
                    if (packet.getYRot(0) < 360 && packet.getYRot(0) > -360) {
                        WrapperUtils.setPacketYRot(packet, packet.getYRot(0) + 720F);
                    }

                    if (packet.hasRotation())
                        serverRotation = new Rotation(WrapperUtils.getPacketYRot(packet), WrapperUtils.getPacketXRot(packet));
                }
            }
        }
    }

    @EventTarget(Priority.HIGHEST)
    public void onBlink(EventBlink e) {
        if (e.getPacket() instanceof ServerboundMovePlayerPacket wrapper && Disabler.instance.isEnable() && Disabler.instance.grim.getValue() && Disabler.instance.aim360.getValue()) {
            if (!e.isCancelled()) {
                if (wrapper.getYRot(0) < 360 && wrapper.getYRot(0) > -360) {
                    WrapperUtils.setPacketYRot(wrapper, wrapper.getYRot(0) + 720F);
                }
            }
        }
    }

    @EventTarget(Priority.LOWEST)
    public void onLook(EventLook eventLook) {
        if (active && rotations != null) {
            if (lastServerRotations != null &&
                    Math.abs(rotations.getYaw() - lastServerRotations.getYaw()) < 0.001f &&
                    Math.abs(rotations.getPitch() - lastServerRotations.getPitch()) < 0.001f) {
                float yaw = rotations.getYaw() + (float) ((Math.random() - 0.5) * 0.001);
                float pitch = rotations.getPitch() + (float) ((Math.random() - 0.5) * 0.001);
                eventLook.setYaw(yaw);
                pitch = MathUtils.clampPitch_To90(pitch);
                eventLook.setPitch(pitch);
            } else {

                eventLook.setYaw(rotations.getYaw());
                eventLook.setPitch(rotations.getPitch());
            }
        }
    }

    @EventTarget(Priority.LOWEST)
    public void onTick(EventTick event) {
        if (active && rotations != null) {
            mc.player.setYHeadRot(rotations.getYaw());
            mc.player.setYBodyRot(rotations.getYaw());
        }
    }

    @EventTarget
    public void onMotion(EventMotion event) {
        if (event.getEventType() == EventType.PRE) {
            if (active && rotations != null) {
                smoothed = true;

                float yaw = rotations.getYaw();
                float pitch = rotations.getPitch();
                if (randomRotation && lastServerRotations != null &&
                        Math.abs(yaw - lastServerRotations.getYaw()) < 0.001f &&
                        Math.abs(pitch - lastServerRotations.getPitch()) < 0.001f) {
                    yaw += (float) ((Math.random() - 0.5) * 0.001);
                    pitch += (float) ((Math.random() - 0.5) * 0.001);
                }
                pitch = MathUtils.clampPitch_To90(pitch);
                event.setYaw(Rotation.getFixedYaw(yaw));
                event.setPitch(pitch);
                mc.player.yHeadRot = yaw;

//                ChatUtils.displayAlert("Rotation: " + rotations.getYaw());
                lastServerRotations = new Rotation(yaw, pitch);

                lastRotations = rotations;
            } else {
                lastRotations = new Rotation(mc.player.getYRot(), mc.player.getXRot());
            }
            targetRotations = new Rotation(mc.player.getYRot(), mc.player.getXRot());
        }
    }

    public static void setRotation(Rotation rotations, double rotationSpeed, MovementFix correctMovement, boolean random) {
        rotations.fixedSensitivity(mc.options.sensitivity().get().floatValue());
        if (random)
            targetRotations = PerlinNoiseGenerator.rotation(rotations);
        else
            targetRotations = rotations;
        RotationUtils.rotationSpeed = rotationSpeed * 18;
        RotationUtils.correctMovement = correctMovement;
        active = true;
        smooth();
    }

    public static void setRotation(Rotation rotations, double rotationSpeed, MovementFix correctMovement) {
        setRotation(rotations, rotationSpeed, correctMovement, true);
    }

    public static double direction(float rotationYaw, final double moveForward, final double moveStrafing) {
        if (moveForward < 0F) rotationYaw += 180F;

        float forward = 1F;

        if (moveForward < 0F) forward = -0.5F;
        else if (moveForward > 0F) forward = 0.5F;

        if (moveStrafing > 0F) rotationYaw -= 90F * forward;
        if (moveStrafing < 0F) rotationYaw += 90F * forward;

        return Math.toRadians(rotationYaw);
    }

    public static double direction() {
        float rotationYaw = clampAngle(mc.player.getYRot());
        if (mc.player.zza < 0f) rotationYaw += 180f;
        float forward = 1f;
        if (mc.player.zza < 0f) {
            forward = -0.5f;
        } else if (mc.player.zza > 0f) {
            forward = 0.5f;
        }
        if (mc.player.xxa > 0f) rotationYaw -= 70f * forward;
        if (mc.player.xxa < 0f) rotationYaw += 70f * forward;
        return Math.toRadians(rotationYaw);
    }

    public static Rotation applySensitivityPatch(Rotation rotation) {
        final Rotation previousRotation = new Rotation(WrapperUtils.getYRotLast(), WrapperUtils.getXRotLast());
        final double mouseSensitivity = mc.options.sensitivity().get() * 0.6F + 0.2F;
        final double multiplier = mouseSensitivity * mouseSensitivity * mouseSensitivity * 8.0F * 0.15D;
        final float yaw = previousRotation.getYaw() + (float) (Math.round((rotation.getYaw() - previousRotation.getYaw()) / multiplier) * multiplier);
        final float pitch = previousRotation.getPitch() + (float) (Math.round((rotation.getPitch() - previousRotation.getPitch()) / multiplier) * multiplier);
        return new Rotation(yaw, Mth.clamp(pitch, -90, 90));
    }

    public static Rotation applySensitivityPatch(Rotation rotation, Rotation previousRotation) {
        float mouseSensitivity = (float) (mc.options.sensitivity().get() * (1 + Math.random() / 10000000) * 0.6f + 0.2f);
        double multiplier = mouseSensitivity * mouseSensitivity * mouseSensitivity * 8.0f * 0.15;
        float yaw = (float) (previousRotation.getYaw() + Math.round((rotation.getYaw() - previousRotation.getYaw()) / multiplier) * multiplier);
        float pitch = (float) (previousRotation.getPitch() + Math.round((rotation.getPitch() - previousRotation.getPitch()) / multiplier) * multiplier);
        pitch = Mth.clamp(pitch, -90f, 90f);
        return new Rotation(yaw, pitch);
    }

    public static void smooth() {
        if (smoothed) {
//            final float lastYaw = lastRotations.getFixedYaw();
//            final float lastPitch = lastRotations.getFixedPitch();
//            final float targetYaw = targetRotations.getFixedYaw();
//            final float targetPitch = targetRotations.getFixedPitch();
            Rotation fixedLastRotation = Rotation.getFixedRotation(lastRotations);
            Rotation fixedTargetRotation = Rotation.getFixedRotation(targetRotations);
            //  rotations = smooth(ClientSetting.rotationFix.getValue() ? fixedLastRotation : lastRotations, ClientSetting.rotationFix.getValue() ? fixedTargetRotation : targetRotations, rotationSpeed + Math.random());
            //            rotations = getSmoothRotation(lastRotations, targetRotations, (float) (rotationSpeed + Math.random()));
//            ChatUtils.displayAlert("YAW "+rotations.getYaw());
            rotations = targetRotations;
        }

        smoothed = false;
        mc.gameRenderer.pick(1.0f);
    }

    public static Rotation smooth(final Rotation lastRotation, final Rotation targetRotation, final double speed) {
        float yaw = targetRotation.getYaw();
        float pitch = targetRotation.getPitch();
        final float lastYaw = lastRotation.getYaw();
        final float lastPitch = lastRotation.getPitch();

        if (speed != 0) {
            final float rotationSpeed = (float) speed;
            final double deltaYaw = Mth.wrapDegrees(targetRotation.getYaw() - lastRotation.getYaw());
            final double deltaPitch = pitch - lastPitch;
            final double distance = Math.sqrt(deltaYaw * deltaYaw + deltaPitch * deltaPitch);
            final double distributionYaw = Math.abs(deltaYaw / distance);
            final double distributionPitch = Math.abs(deltaPitch / distance);
            final double maxYaw = rotationSpeed * distributionYaw;
            final double maxPitch = rotationSpeed * distributionPitch;
            final float moveYaw = (float) Math.max(Math.min(deltaYaw, maxYaw), -maxYaw);
            final float movePitch = (float) Math.max(Math.min(deltaPitch, maxPitch), -maxPitch);
            yaw = lastYaw + moveYaw;
            pitch = lastPitch + movePitch;
            int fps = ObfuscationReflectionHelper.getPrivateValue(Minecraft.class, mc, "fps");
            for (int i = 1; i <= (int) (fps / 20f + Math.random() * 10); ++i) {
                if (Math.abs(moveYaw) + Math.abs(movePitch) > 1) {
                    yaw += (float) ((Math.random() - 0.5) / 1000);
                    pitch -= (float) (Math.random() / 200);
                }

                final Rotation rotations = new Rotation(yaw, pitch);
                final Rotation fixedRotations = applySensitivityPatch(rotations);
                yaw = fixedRotations.getYaw();
                pitch = Math.max(-90, Math.min(90, fixedRotations.getPitch()));
            }
        }
        if (randomRotation) {
            if (Math.abs(yaw - lastYaw) < 0.001f && Math.abs(pitch - lastPitch) < 0.001f) {
                yaw += (float) ((Math.random() - 0.5) * 0.001);
                pitch += (float) ((Math.random() - 0.5) * 0.001);
            }
        }


        return new Rotation(yaw, pitch);
    }

    public static Rotation calculateWithGravity(final Vec3 from, final Vec3 to, double g, double v) {
        final Vec3 diff = to.subtract(from);

        final double dx = diff.x;
        final double dz = diff.z;
        final double dy = diff.y;
        float pull = 1f;
        final double horizontalDistance = Math.hypot(dx, dz);
        final double gravity = g;
        final double velocity = pull * v; // 弓箭最大速度为 3.0

        // 使用抛物线解法：计算 pitch 所需的角度解
        double velocitySq = velocity * velocity;
        double underRoot = velocitySq * velocitySq
                - gravity * (gravity * horizontalDistance * horizontalDistance + 2 * dy * velocitySq);

        if (underRoot < 0) {
            // 无解，目标太远或角度过高
            return null;
        }

        // 选择较平的射击角度（较低的解）
        double sqrt = Math.sqrt(underRoot);
        double angleRadians = Math.atan((velocitySq - sqrt) / (gravity * horizontalDistance));

        // Yaw 不受重力影响，按水平方向计算
        float yaw = (float) (Mth.atan2(dz, dx) * (180.0 / Math.PI)) - 90.0F;
        float pitch = (float) -Math.toDegrees(angleRadians);
        return new Rotation(yaw, pitch);
    }

    public static double getDistanceToEntityBox(Entity target) {
        Vec3 eyes = mc.player.getEyePosition();
        Vec3 closest = getNearestPointInAABB(eyes, target.getBoundingBox());

        double xDist = Math.abs(closest.x - eyes.x);
        double yDist = Math.abs(closest.y - eyes.y);
        double zDist = Math.abs(closest.z - eyes.z);

        return Math.sqrt(xDist * xDist + yDist * yDist + zDist * zDist);
    }

    public static Vec3 getNearestPointInAABB(Vec3 eye, AABB box) {
        double[] origin = new double[]{eye.x, eye.y, eye.z};
        double[] destMins = new double[]{box.minX, box.minY, box.minZ};
        double[] destMaxs = new double[]{box.maxX, box.maxY, box.maxZ};

        for (int i = 0; i <= 2; i++) {
            if (origin[i] > destMaxs[i]) {
                origin[i] = destMaxs[i];
            } else if (origin[i] < destMins[i]) {
                origin[i] = destMins[i];
            }
        }

        return new Vec3(origin[0], origin[1], origin[2]);
    }

    public static float getAngleDifference(final float a, final float b) {
        return ((((a - b) % 360F) + 540F) % 360F) - 180F;
    }

    public static Rotation getAngles(Entity entity) {
        if (entity == null) return null;

        final double diffX = entity.getX() - mc.player.getX(),
                diffY = entity.getY() + entity.getEyeHeight() * 0.9 - (mc.player.getY() + mc.player.getEyeHeight()),
                diffZ = entity.getZ() - mc.player.getZ();
        final double dist = Mth.sqrt((float) (diffX * diffX + diffZ * diffZ));
        final float yaw = (float) (Math.atan2(diffZ, diffX) * 180.0D / Math.PI) - 90.0F;
        final float pitch = (float) -(Math.atan2(diffY, dist) * 180.0D / Math.PI);

        return new Rotation(
                mc.player.getYRot() + Mth.wrapDegrees(yaw - mc.player.getYRot()),
                mc.player.getXRot() + Mth.wrapDegrees(pitch - mc.player.getXRot())
        );
    }

    public static double getRotationDifference(Entity entity, float targetYaw, float targetPitch) {
        float yawDiff = Math.abs(entity.getYRot() - targetYaw) % 360F;
        if (yawDiff > 180F) {
            yawDiff = 360F - yawDiff;
        }
        float pitchDiff = Math.abs(entity.getXRot() - targetPitch);
        return yawDiff + pitchDiff;
    }

    public static double getRotationDifference(final Rotation a, final Rotation b) {
        return Math.hypot(getAngleDifference(a.getYaw(), b.getYaw()), a.getPitch() - b.getPitch());
    }

    public static double getRotationDifference(final Rotation rotation) {
        return lastRotations == null ? 0D : getRotationDifference(rotation, lastRotations);
    }

    public static double getRotationDifference(final LivingEntity entity) {
        float playerYaw = clampAngle(mc.player.getYRot());
        float playerPitch = mc.player.getXRot();
        double entityX = entity.getX();
        double entityY = entity.getY() + (entity.getBbHeight() / 2);
        double entityZ = entity.getZ();
        double deltaX = entityX - mc.player.getX();
        double deltaY = entityY - (mc.player.getY() + mc.player.getEyeHeight());
        double deltaZ = entityZ - mc.player.getZ();
        double targetYaw = Math.toDegrees(Math.atan2(-deltaX, deltaZ));
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        double targetPitch = Math.toDegrees(Math.atan2(-deltaY, distance));

        double yawDifference = Math.abs(playerYaw - targetYaw) % 360;
        if (yawDifference > 180) {
            yawDifference = 360 - yawDifference;
        }

        double pitchDifference = Math.abs(playerPitch - targetPitch) % 360;
        if (pitchDifference > 180) {
            pitchDifference = 360 - pitchDifference;
        }

        return Math.sqrt(yawDifference * yawDifference + pitchDifference * pitchDifference);
    }

    public static Rotation toRotation(Vec3 vec, boolean predict) {
        Vec3 eyesPos = new Vec3(mc.player.getX(), mc.player.getBoundingBox().minY + mc.player.getEyeHeight(), mc.player.getZ());

        if (predict) {
            eyesPos = eyesPos.add(mc.player.getX(), mc.player.getY(), mc.player.getZ());
        }

        double diffX = vec.x - eyesPos.x;
        double diffY = vec.y - eyesPos.y;
        double diffZ = vec.z - eyesPos.z;

        float yaw = Mth.wrapDegrees((float) (Math.toDegrees(Math.atan2(diffZ, diffX)) - 90.0));
        float pitch = Mth.wrapDegrees((float) -Math.toDegrees(Math.atan2(diffY, Math.sqrt(diffX * diffX + diffZ * diffZ))));

        return new Rotation(yaw, pitch);
    }

    public static Rotation getSmoothRotations(LivingEntity entity) {
        double x = entity.getX() - mc.player.getX();
        double z = entity.getZ() - mc.player.getZ();
        double y = (entity.getY() + entity.getEyeHeight() - (mc.player.getY() + mc.player.getEyeHeight()));

        double d3 = Math.sqrt(x * x + z * z);

        float yaw = (float) Math.toDegrees(Math.atan2(z, x)) - 90.0f;
        float pitch = (float) -Math.toDegrees(Math.atan2(y, d3));

        return new Rotation(yaw, pitch);
    }

    public static Rotation getRotationsByVec(Vec3 origin, Vec3 position) {
        final Vec3 difference = position.subtract(origin);
        final double distance = new Vec3(difference.x, 0.0, difference.z).length();
        final float yaw = (float) Math.toDegrees(Math.atan2(difference.z, difference.x)) - 90.0f;
        final float pitch = (float) (-Math.toDegrees(Math.atan2(difference.y, distance)));
        return new Rotation(yaw, pitch);
    }

    public static Rotation getBestRotation(BlockPos blockPos, Direction face) {
        Vec3i faceVec = face.getNormal();

        float minX, maxX, minY, maxY, minZ, maxZ;

        if (faceVec.getX() == 0) {
            minX = 0.1f;
            maxX = 0.9f;
        } else if (faceVec.getX() > 0) {
            minX = maxX = 1.0f;
        } else {
            minX = maxX = 0.0f;
        }

        if (faceVec.getY() == 0) {
            minY = 0.1f;
            maxY = 0.9f;
        } else if (faceVec.getY() > 0) {
            minY = maxY = 1.0f;
        } else {
            minY = maxY = 0.0f;
        }

        if (faceVec.getZ() == 0) {
            minZ = 0.1f;
            maxZ = 0.9f;
        } else if (faceVec.getZ() > 0) {
            minZ = maxZ = 1.0f;
        } else {
            minZ = maxZ = 0.0f;
        }

        float[] bestRot = RotationUtils.getRotations(blockPos);
        Rotation rotation = new Rotation(bestRot[0], bestRot[1]);
        double bestDist = RotationUtils.getRotationDifference(rotation); // Pass the rotation array

        Vec3 blockBaseVec = new Vec3(blockPos.getX(), blockPos.getY(), blockPos.getZ());

        for (float x = minX; x <= maxX + 0.001f; x += 0.1f) {
            for (float y = minY; y <= maxY + 0.001f; y += 0.1f) {
                for (float z = minZ; z <= maxZ + 0.001f; z += 0.1f) {
                    Vec3 candidateLocal = new Vec3(x, y, z);
                    Vec3 candidateWorld = blockBaseVec.add(candidateLocal);

                    double diff = RotationUtils.getRotationDifference(candidateWorld);
                    if (diff < bestDist) {
                        bestDist = diff;
                        bestRot = RotationUtils.getRotations(candidateWorld);
                    }
                }
            }
        }
        return new Rotation(bestRot[0], bestRot[1]);
    }

    public static double getRotationDifference(Vec3 targetPoint) {
        Player player = mc.player;
        if (player == null) {
            return Double.MAX_VALUE;
        }
        float[] targetRots = getRotations(targetPoint);
        return getRotationDifference(player, targetRots[0], targetRots[1]);
    }

    public static float[] getRotations(BlockPos blockPos) {
        return getRotations(Vec3.atCenterOf(blockPos)); // Vec3.atCenterOf(blockPos) 获取方块中心点
    }

    public static float[] getRotations(Vec3 target) {
        Player player = mc.player;
        if (player == null) {
            return new float[]{0, 0};
        }
        Vec3 eyePos = player.getEyePosition(1.0F); // 1.0F for partial ticks, for smoother visuals

        double dx = target.x - eyePos.x;
        double dy = target.y - eyePos.y; // 目标点Y - 玩家眼睛Y
        double dz = target.z - eyePos.z;

        double horizontalDistance = Math.sqrt(dx * dx + dz * dz);

        float yaw = (float) (Math.toDegrees(Math.atan2(dz, dx)) - 90.0F);
        float pitch = (float) -Math.toDegrees(Math.atan2(dy, horizontalDistance));

        return new float[]{yaw, pitch};
    }

    public static Rotation getRotationBlock(BlockPos pos) {
        Vec3 playerPos = mc.player.position().add(0.0, mc.player.getEyeHeight(), 0.0);
        Vec3 blockPos = new Vec3(pos.getX() + 0.51, pos.getY() + 0.51, pos.getZ() + 0.51);
        return getRotationsByVec(playerPos, blockPos);
    }

    public static HitResult performRaytrace(Rotation rotation, float reach) {
        if (mc.player == null || mc.level == null) {
            return null;
        }

        Vec3 eyes = mc.player.getEyePosition(1.0f);
        Vec3 rotationVec = getVectorForRotation(rotation);
        Vec3 endVec = eyes.add(rotationVec.x * reach, rotationVec.y * reach, rotationVec.z * reach);
        return mc.level.clip(new ClipContext(eyes, endVec,
                ClipContext.Block.OUTLINE, ClipContext.Fluid.NONE, mc.player));
    }

    public static Vec3 getVectorForRotation(Rotation rotation) {
        return getVectorForRotation(rotation.getYaw(), rotation.getPitch());
    }

    public static boolean canWallAttack(Entity targetEntity, double range, Rotation rotation) {
        if (mc.player == null || mc.level == null) return false;

        Vec3 eyePosition = mc.player.getEyePosition(1.0F);
        Vec3 lookVec = RotationUtils.getVectorForRotation(rotation.getYaw(), rotation.getPitch());
        Vec3 rayEnd = eyePosition.add(lookVec.scale(range));

        BlockHitResult hitResult = mc.level.clip(new ClipContext(
                eyePosition,
                rayEnd,
                ClipContext.Block.COLLIDER,
                ClipContext.Fluid.NONE,
                mc.player
        ));

        if (hitResult.getType() != HitResult.Type.BLOCK) {
            return true;
        }

        double hitDistance = eyePosition.distanceTo(hitResult.getLocation());
        double targetDistance = eyePosition.distanceTo(targetEntity.getBoundingBox().getCenter());

        return hitDistance >= targetDistance;
    }

    public static Vec3 getNearestPointBB(final Vec3 eye, final AABB box) {
        final double[] origin = {eye.x, eye.y, eye.z};
        final double[] destMins = {box.minX, box.minY, box.minZ};
        final double[] destMaxs = {box.maxX, box.maxY, box.maxZ};
        for (int i = 0; i < 3; ++i) {
            if (origin[i] > destMaxs[i]) {
                origin[i] = destMaxs[i];
            } else if (origin[i] < destMins[i]) {
                origin[i] = destMins[i];
            }
        }
        return new Vec3(origin[0], origin[1], origin[2]);
    }

    public static Map<BlockPos, Block> searchBlocks(int radius) {
        Map<BlockPos, Block> blocks = new HashMap<>();
        BlockPos playerPos = mc.player != null ? new BlockPos((int) mc.player.getX(), (int) mc.player.getY(), (int) mc.player.getZ()) : BlockPos.ZERO;

        for (int x = radius; x >= -radius + 1; x--) {
            for (int y = radius; y >= -radius + 1; y--) {
                for (int z = radius; z >= -radius + 1; z--) {
                    BlockPos blockPos = playerPos.offset(x, y, z);
                    Block block = BlockUtils.getBlock(blockPos);

                    blocks.put(blockPos, block);
                }
            }
        }

        return blocks;
    }

    public static boolean wallAttack(boolean throughWalls, Entity entity) {
        AABB bb = entity.getBoundingBox();
        double xSearch = 0.15;
        while (xSearch < 0.85) {
            double ySearch = 0.15;
            while (ySearch < 1.0) {
                double zSearch = 0.15;
                while (zSearch < 0.85) {
                    Vec3 vec3 = new Vec3(
                            bb.minX + (bb.maxX - bb.minX) * xSearch,
                            bb.minY + (bb.maxY - bb.minY) * ySearch,
                            bb.minZ + (bb.maxZ - bb.minZ) * zSearch
                    );

                    if (throughWalls || isVisible(vec3))
                        return true;

                    zSearch += 0.1;
                }
                ySearch += 0.1;
            }
            xSearch += 0.1;
        }
        return false;
    }

    public static boolean isVisible(Vec3 vec3) {
        Vec3 eyesPos = new Vec3(
                mc.player.getX(),
                mc.player.getY() + mc.player.getEyeHeight(),
                mc.player.getZ()
        );

        return mc.level.clip(new ClipContext(eyesPos, vec3, ClipContext.Block.OUTLINE, ClipContext.Fluid.NONE, mc.player)).getType() == HitResult.Type.MISS;
    }

    public static Rotation getHVHRotation(final Entity entity) {
        if (entity == null) {
            return null;
        }

        final double diffX = entity.getX() - mc.player.getX();
        final double diffZ = entity.getZ() - mc.player.getZ();
        final Vec3 BestPos = RotationUtils.getNearestPointBB(mc.player.getEyePosition(1.0f), entity.getBoundingBox());
        final Location myEyePos = new Location(mc.player.getX(), mc.player.getY() + mc.player.getEyeHeight(), mc.player.getZ());
        final double diffY = BestPos.y - myEyePos.getY();
        final double dist = Mth.sqrt((float) (diffX * diffX + diffZ * diffZ));
        final float yaw = (float) (Math.atan2(diffZ, diffX) * 180.0 / 3.141592653589793) - 90.0f;
        final float pitch = (float) (-(Math.atan2(diffY, dist) * 180.0 / 3.141592653589793));
        return new Rotation(yaw, pitch);
    }

    public static Rotation getCustomRotation(Vec3 vec) {
        Vec3 playerVector = new Vec3(mc.player.getX(), mc.player.getY() + mc.player.getEyeHeight(), mc.player.getZ());
        double y = vec.y - playerVector.y;
        double x = vec.x - playerVector.x;
        double z = vec.z - playerVector.z;
        double dff = Math.sqrt(x * x + z * z);
        float yaw = (float) Math.toDegrees(Math.atan2(z, x)) - 90.0f;
        float pitch = (float) (-Math.toDegrees(Math.atan2(y, dff)));
        return new Rotation(Mth.wrapDegrees(yaw), Mth.wrapDegrees(pitch));
    }

    public static Vec3 getLocation(AABB bb) {
        double yaw = 0.5;
        double pitch = 0.5;
        return new Vec3(
                bb.minX + (bb.maxX - bb.minX) * yaw,
                bb.minY + (bb.maxY - bb.minY) * pitch,
                bb.minZ + (bb.maxZ - bb.minZ) * yaw
        );
    }

    public static Rotation getSmoothRotations2(Entity entity) {
        double x = entity.getX() - mc.player.getX();
        double z = entity.getZ() - mc.player.getZ();
        double y = entity.getY() + entity.getEyeHeight() - (mc.player.getBoundingBox().minY + (mc.player.getBoundingBox().maxY - mc.player.getBoundingBox().minY));

        double d3 = Math.sqrt(x * x + z * z);
        float yaw = (float) (Mth.atan2(z, x) * 180.0 / Math.PI) - 90.0F;
        float pitch = (float) (-(Mth.atan2(y, d3) * 180.0 / Math.PI));

        return new Rotation(yaw, pitch);
    }

    public static float smoothRotation(float from, float to, float speed) {
        float f = Mth.wrapDegrees(to - from);

        if (f > speed) {
            f = speed;
        }

        if (f < -speed) {
            f = -speed;
        }

        return from + f;
    }

    public static Rotation calculate(final Entity entity) {
        return calculate(getCustomPositionVector(entity).add(0, Math.max(0, Math.min(mc.player.getY() - entity.getY() +
                mc.player.getEyeHeight(), (entity.getBoundingBox().maxY - entity.getBoundingBox().minY) * 0.9)), 0));
    }

    public static Rotation calculate(Vector3d to) {
        return calculate(getCustomPositionVector(mc.player).add(0.0, mc.player.getEyeHeight(), 0.0), to);
    }

    public static Rotation calculate(Vector3d from, Vector3d to) {
        Vector3d diff = to.subtract(from);
        double distance = Math.hypot(diff.x(), diff.z());
        float yaw = (float) (Mth.atan2(diff.z(), diff.x()) * TO_DEGREES) - 90.0f;
        float pitch = (float) (-(Mth.atan2(diff.y(), distance) * TO_DEGREES));
        return new Rotation(yaw, pitch);
    }

    public static Vector3d getCustomPositionVector(Entity entity) {
        return new Vector3d(entity.getX(), entity.getY(), entity.getZ());
    }

    public static Vec3 getRtxPoint(float yaw, float pitch, float distance) {
        Vec3 vec3d = mc.player.position().add(0, mc.player.getEyeHeight(mc.player.getPose()), 0);
        double distancePow2 = Math.pow(distance, 2);
        Vec3 vec3d2 = getRotationVector(pitch, yaw);
        Vec3 vec3d3 = vec3d.add(vec3d2.x * distance, vec3d2.y * distance, vec3d2.z * distance);
        AABB box = mc.player.getBoundingBox().expandTowards(vec3d2.add(vec3d2.x * distance, vec3d2.y * distance, vec3d2.z * distance)).inflate(1.0, 1.0, 1.0);
        EntityHitResult entityHitResult = ProjectileUtil.getEntityHitResult(mc.player, vec3d, vec3d3, box, (entity) -> !entity.isSpectator(), distancePow2);
        if (entityHitResult != null) {
            Entity entity2 = entityHitResult.getEntity();
            Vec3 vec3d4 = entityHitResult.getLocation();
            if (entity2 instanceof LivingEntity) {
                return vec3d4;
            }
        }
        return null;
    }

    public static Vec3 getRotationVector(float yaw, float pitch) {
        return new Vec3(Mth.sin(-pitch * 0.017453292F) * Mth.cos(yaw * 0.017453292F), -Mth.sin(yaw * 0.017453292F), Mth.cos(-pitch * 0.017453292F) * Mth.cos(yaw * 0.017453292F));
    }

    public static Rotation lookAtEntity(Entity target) {
        double playerX = mc.player.getX();
        double playerY = mc.player.getEyeY();
        double playerZ = mc.player.getZ();
        double targetX = target.getX();
        double targetY = target.getY() + (target.getBbHeight() / 2);
        double targetZ = target.getZ();
        double deltaX = targetX - playerX;
        double deltaY = targetY - playerY;
        double deltaZ = targetZ - playerZ;
        double distanceXZ = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        float yaw = (float) (Math.atan2(deltaZ, deltaX) * (180 / Math.PI)) - 90;
        float pitch = (float) -(Math.atan2(deltaY, distanceXZ) * (180 / Math.PI));
        return new Rotation(yaw, pitch);
    }

    public static Rotation getRotationFromEyeToPoint(Vector3d point3d) {
        return getRotation(new Vector3d(mc.player.getX(), mc.player.getBoundingBox().minY + (double) mc.player.getEyeHeight(), mc.player.getZ()), point3d);
    }

    public static Rotation getRotation(Vector3d from, Vector3d to) {
        double x = to.x() - from.x();
        double y = to.y() - from.y();
        double z = to.z() - from.z();
        double sqrt = Math.sqrt(x * x + z * z);
        float yaw = (float) Math.toDegrees(Math.atan2(z, x)) - 90.0F;
        float pitch = (float) (-Math.toDegrees(Math.atan2(y, sqrt)));
        return new Rotation(yaw, Math.min(Math.max(pitch, -90.0F), 90.0F));
    }

    public static Rotation getSimpleRotations(Entity target) {
        double yDist = target.getY() - mc.player.getY();
        Vector3d targetPos;
        if (yDist >= 1.547) {
            targetPos = new Vector3d(target.getX(), target.getY(), target.getZ());
        } else if (yDist <= -1.547) {
            targetPos = new Vector3d(target.getX(), target.getY() + (double) target.getEyeHeight(), target.getZ());
        } else {
            targetPos = new Vector3d(target.getX(), target.getY() + (double) (target.getEyeHeight() / 2.0F), target.getZ());
        }

        return getRotationFromEyeToPoint(targetPos);
    }


    public static boolean isLookingAtBlock(Rotation rotation, BlockPos blockPos, double maxDistance, float exact) {
        float yaw = rotation.getYaw();
        float pitch = rotation.getPitch();
        Vec3 playerEyePos = mc.player.getEyePosition(1.0F);
        double lookX = -Math.sin(Math.toRadians(yaw)) * Math.cos(Math.toRadians(pitch));
        double lookY = -Math.sin(Math.toRadians(pitch));
        double lookZ = Math.cos(Math.toRadians(yaw)) * Math.cos(Math.toRadians(pitch));
        Vec3 lookDirection = new Vec3(lookX, lookY, lookZ).normalize();
        Vec3 blockCenterPos = new Vec3(blockPos.getX() + 0.5, blockPos.getY() + 0.5, blockPos.getZ() + 0.5);
        double distanceToBlock = playerEyePos.distanceTo(blockCenterPos);
        if (distanceToBlock <= maxDistance) {
            Vec3 toBlockVector = blockCenterPos.subtract(playerEyePos).normalize();
            double dotProduct = lookDirection.dot(toBlockVector);
            return dotProduct >= exact;
        }

        return false;
    }


    public static Vec3 getVectorForRotation(float yaw, float pitch) {
        float f = Mth.cos(-yaw * 0.017453292F - (float) Math.PI);
        float f1 = Mth.sin(-yaw * 0.017453292F - (float) Math.PI);
        float f2 = -Mth.cos(-pitch * 0.017453292F);
        float f3 = Mth.sin(-pitch * 0.017453292F);
        return new Vec3(f1 * f2, f3, f * f2);
    }

    public static HitResult rayTraceCustom(Entity entity, double blockReachDistance, float partialTicks, float yaw, float pitch) {
        final Vec3 vec3 = entity.getEyePosition(partialTicks);
        final Vec3 vec31 = getVectorForRotation(yaw, pitch);
        final Vec3 vec32 = vec3.add(vec31.x * blockReachDistance, vec31.y * blockReachDistance, vec31.z * blockReachDistance);
        return mc.level.clip(new ClipContext(vec3, vec32, ClipContext.Block.OUTLINE, ClipContext.Fluid.NONE, entity));
    }


    public static int clampAngle(float angle) {
        return Math.floorMod((int) angle, 360);
    }

    public static Rotation calculateSmoothLookAt(BlockPos targetPos, float maxTurnSpeed) {
        float smoothYaw = 0, smoothPitch = 0;
        Vec3 blockCenter = new Vec3(targetPos.getX() + 0.5, targetPos.getY() + 0.5, targetPos.getZ() + 0.5);
        Vec3 playerEyes = mc.player.getEyePosition(1.0F);
        double dx = blockCenter.x - playerEyes.x;
        double dy = blockCenter.y - playerEyes.y;
        double dz = blockCenter.z - playerEyes.z;
        float targetYaw = (float) Math.toDegrees(Math.atan2(-dx, dz));
        float distance = (float) Math.sqrt(dx * dx + dz * dz); // 水平距离
        float targetPitch = (float) Math.toDegrees(Math.atan2(-dy, distance));
        smoothYaw = interpolateAngle(smoothYaw, targetYaw, maxTurnSpeed);
        smoothPitch = interpolateAngle(smoothPitch, targetPitch, maxTurnSpeed / 2);
        smoothYaw += (float) ((Math.random() - 0.5) * 0.5F);
        smoothPitch += (float) ((Math.random() - 0.5) * 0.5F);
        return new Rotation(smoothYaw, smoothPitch);
    }

    private static float interpolateAngle(float current, float target, float maxSpeed) {
        float delta = wrapDegrees(target - current);
        if (delta > maxSpeed) delta = maxSpeed;
        if (delta < -maxSpeed) delta = -maxSpeed;
        return current + delta;
    }

    private static float wrapDegrees(float angle) {
        while (angle > 180.0F) angle -= 360.0F;
        while (angle < -180.0F) angle += 360.0F;
        return angle;
    }

    public static float getRotations(Entity entity) {
        if (mc.player == null)
            return 0;

        double x = interp(entity.position().x, entity.xOld) - interp(mc.player.position().x, mc.player.xOld);
        double z = interp(entity.position().z, entity.zOld) - interp(mc.player.position().z, mc.player.zOld);
        return (float) -(Math.atan2(x, z) * (180 / Math.PI));
    }

    public static double interp(double d, double d2) {
        return d2 + (d - d2) * (double) Render3DEngine.getTickDelta();
    }

    public static boolean canWallAttack(Entity targetEntity, double range) {
        Vec3 eyePosition = mc.player.getEyePosition(1.0F);
        Vec3 targetPosition = targetEntity.getBoundingBox().getCenter();
        BlockHitResult hitResult = mc.level.clip(new ClipContext(
                eyePosition,
                targetPosition,
                ClipContext.Block.COLLIDER,
                ClipContext.Fluid.NONE,
                mc.player
        ));

        if (hitResult.getType() != BlockHitResult.Type.BLOCK)
            return true;

        double wallDistance = eyePosition.distanceTo(hitResult.getLocation());
        return wallDistance < range;
    }

    public static Direction getViewDirection(Rotation rotation, BlockPos pos) {
        float yaw = rotation.getYaw();
        float pitch = rotation.getPitch();
        double pitchRad = Math.toRadians(pitch);
        double yawRad = Math.toRadians(yaw);
        double x = -Math.sin(yawRad) * Math.cos(pitchRad);
        double y = -Math.sin(pitchRad);
        double z = Math.cos(yawRad) * Math.cos(pitchRad);
        double dx = pos.getX();
        double dy = pos.getY();
        double dz = pos.getZ();
        double dotX = x * dx;
        double dotY = y * dy;
        double dotZ = z * dz;
        double absX = Math.abs(dotX);
        double absY = Math.abs(dotY);
        double absZ = Math.abs(dotZ);

        if (absY > absX && absY > absZ) {
            return dotY > 0 ? Direction.DOWN : Direction.UP;
        } else if (absX > absZ) {
            return dotX > 0 ? Direction.WEST : Direction.EAST;
        } else {
            return dotZ > 0 ? Direction.NORTH : Direction.SOUTH;
        }
    }
}